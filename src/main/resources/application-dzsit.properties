# =============================================
# 数据库配置
# =============================================
# 数据库连接信息
spring.datasource.druid.username=admin
spring.datasource.druid.password=r1RGAwWt8dJJj+wt
spring.datasource.druid.url=********************************************************************************************************************************************************************************************************************************
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver

# =============================================
# Redis配置
# =============================================
# Redis连接信息
spring.redis.host=**************
spring.redis.port=6379
spring.redis.password=SmKJxOpFka3O7V/b
spring.redis.database=0
spring.redis.timeout=10000
spring.redis.lettuce.pool.min-idle=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.max-active=32

# =============================================
# 钉钉配置
# =============================================
# 钉钉应用配置
ding.talk.app.key=dingxplq9fb6xjvmhi8y
ding.talk.app.secret=feeySXbugwG67GXX07J2CdEm3gjlsXaAYkW0a-QOzx6XLfkqiEgu5zy-v-6ElodZ
ding.talk.agent.id=3584984634

# 钉钉审批流配置
# 车辆处置流程
ding.talk.flow.vehicle.disposal.process.code=PROC-F80BE5C1-694D-4C77-A073-ED3CD48205D9
# 车辆报废流程
ding.talk.flow.vehicle.scrap.process.code=PROC-7225F406-8B8C-4C71-B796-DF478669A18E
# 车辆申购流程
ding.talk.flow.vehicle.purchase.process.code=PROC-A4E3BCF3-D9F0-45DF-B0B2-C9389F92E0D7
# 车辆转籍流程
ding.talk.flow.vehicle.transfer.process.code=PROC-CA023E7E-957F-4E97-9567-31E01DBE77E7
# 车辆调拨流程
ding.talk.flow.vehicle.allocate.process.code=PROC-660D040B-F270-4CA1-B52B-9A13984BFFBC
# 车辆业务类型修改流程
ding.talk.flow.vehicle.modify.business.process.code=PROC-8AB855DE-5E24-4D1C-8C1F-52C3F08AE3EB
# 车辆转固流程
ding.talk.flow.vehicle.transfer.fixed.process.code=PROC-55110771-AB37-4A05-BCB8-28E5BC02CD5C
# 车辆逆处置流程
ding.talk.flow.vehicle.reverse.disposal.process.code = PROC-FBAEB742-1C56-436A-98A0-A2E2A4591A84
# 商务业务出售流程
ding.talk.flow.vehicle.business.sell.process.code=PROC-F4FEA6FA-2620-4554-8051-E11561A4FCBF


# =============================================
# SSO配置
# =============================================
# SSO客户端配置
sso.clientId=6226261277652993
rsa.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2sPFl1ThilzByX8ENi/nGrms0YjGzFeo8OBvfBBt/yxPB57SjqWlcmDcEXtCotye8KxOjaXvCnTKJuBUscBkGWAe3AAgIeYdsf1b34gBaB4c5j70nUl6s+X27MPkj2gY5pM7ABa7Kb7qHlNHyNSvWWjqBjn7LiV3DTG+NW0pkC7Q1HzvszfbV280cQuyC5o02N1DgzRJ3CT7VMpo3A7RtqBFiDbNLLDipKeKaIm9iaTR6hRP9qjJHN+5cOvJmeck8FUnvhjyu5e850kSe0dTp2w+/fgu/FBabl8xX9OzcoxwBPJ9aJVips53mm85eOdwWufgcOsANuV8GstFlh6U1QIDAQAB/nc/E0AAvbFCOVEbBNgC/uZES9rQ2n4PGF7uvYiJBmDTskBh2Lv0jZXPFbhlHNCgzARTb8sXfsvYctpWfoaFeB8X8/Toin4cN6nWiplR/G0gEyQCNTHNjUXgHdhIgbv+/dThBMohnDuttw1erlfdfH3dTqHxrbkx2FkSC54sZHcHSbxO8E6iFdcXpduKfusC4EN8rQPBWEPOBeFdyKfU1IKhJM4WL9TLZq4ftFjc9PJpVt84GBmlhRyN81/AuRy7Ciq4dAs8G426FfLjgRs0QIDAQAB
# 登录页面URL
login.url=http://cheguan-test.96822.net/dazhong-transportation-management/index
# SSO Token接口URL
sso.token.url=https://dzjtweb.96822.net/sso/auth/api/v1/login/token

# =============================================
# 文件系统配置
# =============================================
# 文件管理系统配置
file.mfs.url=http://cheguan-test.96822.net/dazhong
file.mfs.root.path=/opt/data/dazhong

# =============================================
# 外部接口配置
# =============================================
# 汽车之家接口配置
model.queryVehicleBaseList.url=https://md-st.evcard.vip/mdpartner/model/queryVehicleBaseList
model.getVehicleBaseInfo.url=https://md-st.evcard.vip/mdpartner/model/getVehicleBaseInfo
# VIN码查询接口配置
vin.getVehicleModelInfo.url=https://api.tanshuapi.com/api/vin/v2/index
# 大众交通接口配置
dzjt.getCaeInfo.url=https://dzjtweb.96822.net/api/public/query/getCarInfo

# =============================================
# 安全配置
# =============================================
# 签名密钥
sign.secretKey=CKKW4MIGyhlLtTnHNcKWk1jtq66n3qGY