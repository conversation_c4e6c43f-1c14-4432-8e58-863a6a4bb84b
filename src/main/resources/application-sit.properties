# =============================================
# 数据库配置
# =============================================
# 数据库连接信息
spring.datasource.druid.username=dzjt
spring.datasource.druid.password=Evcard@1204
spring.datasource.druid.url=************************************************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver

# =============================================
# Redis配置
# =============================================
# Redis连接信息
spring.redis.host=evcard-st-lan.redis.rds.aliyuncs.com
spring.redis.port=6379
spring.redis.password=Wp4uJK*Vc3v2
spring.redis.database = 0
spring.redis.timeout=10000
spring.redis.lettuce.pool.min-idle=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.max-active=32

# =============================================
# 钉钉配置
# =============================================
# 钉钉应用配置
ding.talk.app.key=dingh2s5u35ano13gjji
ding.talk.app.secret=_lhbIIRskg6x5vZ_AoTjgforrigGKpYgagxZzAWG8KE6lcou28fLLlmCvo66BRDg
ding.talk.agent.id=3283332634

# 钉钉审批流配置
ding.talk.flow.vehicle.disposal.process.code=PROC-098C012C-1F54-43A9-B149-B6643CD20625
ding.talk.flow.vehicle.scrap.process.code=PROC-0FD3F55F-AA5C-4E17-AC67-98C4C485D6CD
ding.talk.flow.vehicle.purchase.process.code=PROC-01AB197D-6B76-4B44-82C1-013A915CB6F0
ding.talk.flow.vehicle.transfer.process.code=PROC-966CEDAC-AEDA-4EED-B9C7-75E3587C8798
ding.talk.flow.vehicle.allocate.process.code=PROC-9BF59F35-DD07-47EB-8003-EA22197BACE2
ding.talk.flow.vehicle.modify.business.process.code=PROC-0B0D8942-9F7A-4D48-BAC6-9A34A627C6B1
ding.talk.flow.vehicle.transfer.fixed.process.code=PROC-E1FFA9D3-B381-4403-A05B-D3309C8E6135
# 车辆逆处置流程
ding.talk.flow.vehicle.reverse.disposal.process.code = PROC-6D3F8C66-F9F0-4F1B-BBC1-52425BCA3175
# 商务业务出售流程
ding.talk.flow.vehicle.business.sell.process.code=PROC-29783BC7-6B3D-42DB-967B-CC961BFDFA7A

# =============================================
# SSO配置
# =============================================
# SSO客户端配置
sso.clientId=3829507815438443
rsa.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2sPFl1ThilzByX8ENi/nGrms0YjGzFeo8OBvfBBt/yxPB57SjqWlcmDcEXtCotye8KxOjaXvCnTKJuBUscBkGWAe3AAgIeYdsf1b34gBaB4c5j70nUl6s+X27MPkj2gY5pM7ABa7Kb7qHlNHyNSvWWjqBjn7LiV3DTG+NW0pkC7Q1HzvszfbV280cQuyC5o02N1DgzRJ3CT7VMpo3A7RtqBFiDbNLLDipKeKaIm9iaTR6hRP9qjJHN+5cOvJmeck8FUnvhjyu5e850kSe0dTp2w+/fgu/FBabl8xX9OzcoxwBPJ9aJVips53mm85eOdwWufgcOsANuV8GstFlh6U1QIDAQAB/nc/E0AAvbFCOVEbBNgC/uZES9rQ2n4PGF7uvYiJBmDTskBh2Lv0jZXPFbhlHNCgzARTb8sXfsvYctpWfoaFeB8X8/Toin4cN6nWiplR/G0gEyQCNTHNjUXgHdhIgbv+/dThBMohnDuttw1erlfdfH3dTqHxrbkx2FkSC54sZHcHSbxO8E6iFdcXpduKfusC4EN8rQPBWEPOBeFdyKfU1IKhJM4WL9TLZq4ftFjc9PJpVt84GBmlhRyN81/AuRy7Ciq4dAs8G426FfLjgRs0QIDAQAB
login.url=http://*************/dazhong-transportation-management/index
sso.token.url=https://dzjtweb.96822.net/sso/auth/api/v1/login/token

# =============================================
# 文件系统配置
# =============================================
# 文件管理系统配置
file.mfs.url=https://report-download-st.evcard.vip/dazhong
file.mfs.root.path=/evcard/oms/dazhong

# =============================================
# 外部接口配置
# =============================================
# 汽车之家接口配置
model.queryVehicleBaseList.url=https://md-st.evcard.vip/mdpartner/model/queryVehicleBaseList
model.getVehicleBaseInfo.url=https://md-st.evcard.vip/mdpartner/model/getVehicleBaseInfo
# VIN码查询接口配置
vin.getVehicleModelInfo.url=https://api.tanshuapi.com/api/vin/v2/index
# 大众交通接口配置
dzjt.getCaeInfo.url=https://dztaxi.96822.net/api/public/query/getCarInfo

# =============================================
# 安全配置
# =============================================
# 签名密钥
sign.secretKey=CKKW4MIGyhlLtTnHNcKWk1jtq66n3qGY

# 维修saas同步配置
saas.sync.key=dzjt
saas.sync.vehicle.url=http://saas-sit.gcsrental.com/auto-care-saas/api/v1/data-sync/vehicle-info/batch
saas.sync.model.url=http://saas-sit.gcsrental.com/auto-care-saas/api/v1/data-sync/vehicle-model/batch
saas.sync.org.url=http://saas-sit.gcsrental.com/auto-care-saas/api/v1/data-sync/org-info/batch