package com.dazhong.transportation.vlms.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "车辆表数据")
public class SyncDatabaseVehicleDto {

    @ApiModelProperty(value = "车ID", notes = "vehicleInfo.id")
    private Long id;

    @ApiModelProperty(value = "车牌号", notes = "vehicleInfo.licensePlate")
    private String license;

    @ApiModelProperty(value = "车架号", notes = "vehicleInfo.vin")
    private String frameNo;

    @ApiModelProperty(value = "发动机号", notes = "vehicleInfo.engineNo")
    private String engineNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "车辆出厂日期（产证）", notes = "vehicleInfo.productDate")
    private Date productDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "购买日期", notes = "vehicleInfo.purchaseDate")
    private Date purchaseDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "实际报废日期", notes = "vehicleInfo.realRetirementDate")
    private Date retireDate;

    @ApiModelProperty(value = "使用年限限制", notes = "vehicleInfo.usageAgeLimit")
    private Integer usageAgeLimit;

    @ApiModelProperty(value = "折旧年限限制", notes = "vehicleInfo.depreciationAgeLimit")
    private Integer depreciationAgeLimit;

    @ApiModelProperty(value = "获取方式ID", notes = "vehicleInfo.obtainWayId")
    private Integer obtainWayId;

    @ApiModelProperty(value = "使用性质（行驶证）", notes = "vehicleInfo.usageIdRegistrationCard")
    private Integer usageId;

    @ApiModelProperty(value = "车辆颜色ID", notes = "vehicleInfo.vehicleColorId")
    private Integer vehicleColorId;

    @ApiModelProperty(value = "区域ID", notes = "vehicleInfo.areaId")
    private Long areaId;

    @ApiModelProperty(value = "供应商ID", notes = "vehicleInfo.supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "车辆型号ID", notes = "vehicleInfo.vehicleModelId")
    private Long vehicleModelId;

    @ApiModelProperty(value = "产证编号", notes = "vehicleInfo.certificateNumber")
    private String vehicleRegisterNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "强制报废日期", notes = "vehicleInfo.forceScrapDate")
    private Date forceScrapDate;

    @ApiModelProperty(value = "资产机构ID", notes = "vehicleInfo.ownOrganizationId")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "使用组织ID", notes = "vehicleInfo.usageOrganizationId")
    private Long usageOrganizationId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "启用日期", notes = "vehicleManagementLegacyInfo.startDate")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "发证日期", notes = "vehicleManagementLegacyInfo.licenseDate")
    private Date licenseDate;

    @ApiModelProperty(value = "营运证号", notes = "vehicleManagementLegacyInfo.operatingNo")
    private String operatingNo;

    @ApiModelProperty(value = "车辆拥有公司ID", notes = "vehicleManagementLegacyInfo.ownerId")
    private Integer ownerId;

    @ApiModelProperty(value = "资产车辆拥有公司ID", notes = "vehicleManagementLegacyInfo.assetOwnerId")
    private Integer assetOwnerId;

    @ApiModelProperty(value = "号牌种类ID", notes = "vehicleManagementLegacyInfo.vehicleCategoryId")
    private Integer vehicleCategoryId;

    @ApiModelProperty(value = "运营类型ID", notes = "vehicleManagementLegacyInfo.operateTypeId")
    private Integer operateTypeId;

    @ApiModelProperty(value = "合同类型ID", notes = "vehicleManagementLegacyInfo.contractTypeId")
    private Integer contractTypeId;

    @ApiModelProperty(value = "运营分类ID", notes = "vehicleManagementLegacyInfo.operationCategoryId")
    private Integer operationCategoryId;

    @ApiModelProperty(value = "投入运营日期", notes = "vehicleManagementLegacyInfo.operationStartDate")
    private String putOperateDate;

    @ApiModelProperty(value = "公司车辆拥有公司ID", notes = "vehicleManagementLegacyInfo.companyOwnerId")
    private Integer companyOwnerId;

    @ApiModelProperty(value = "是否有产权证", notes = "vehicleManagementLegacyInfo.hasRight")
    private Integer hasRight;

    @ApiModelProperty(value = "过入单位", notes = "vehicleManagementLegacyInfo.fromCompany")
    private String fromCompany;

    @ApiModelProperty(value = "档案编号", notes = "vehicleManagementLegacyInfo.archivesNo")
    private String archivesNo;

    @ApiModelProperty(value = "车辆资产ID", notes = "vehicleManagementLegacyInfo.vehicleAssetId")
    private Long vehicleAssetId;

    //资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付）  4-待报废（未交付） 5-已处置 6-已报废
    @ApiModelProperty(value = "车辆状态", notes = "vehicleInfo.propertyStatus")
    private Integer propertyStatus;

    @ApiModelProperty(value = "车辆状态-同步数据")
    private Integer vehicleStatus;






}
