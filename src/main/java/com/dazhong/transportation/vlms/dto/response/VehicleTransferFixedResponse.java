package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "车辆转固响应对象")
@Data
public class VehicleTransferFixedResponse implements Serializable {

    @ApiModelProperty(value = "车架号", example = "1HGCM82633A123456")
    private String vin;

    @ApiModelProperty(value = "资产编号", example = "ASSET001")
    private String vehicleAssetId;

    @ApiModelProperty(value = "车型id", example = "2")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名称")
    private String vehicleModelName;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "条线 1-出租车业务线 2-商务长包业务线 3-SVIP业务线 4-网约车业务线 5-新快车业务线", example = "1")
    private Integer productLine;

    @ApiModelProperty(value = "业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车", example = "1")
    private Integer businessLine;

    @ApiModelProperty(value = "采购申请钉钉号")
    private String approvalNumber;

    @ApiModelProperty(value = "收货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptDate;

    @ApiModelProperty(value = "资产所属公司ID", example = "1")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "资产机构")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "使用机构")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "资产所属公司名称")
    private String assetCompanyName;

    @ApiModelProperty(value = "资产机构")
    private String ownOrganizationName;

    @ApiModelProperty(value = "使用机构")
    private String usageOrganizationName;

    @ApiModelProperty(value = "上牌信息 1-是 2-否", example = "1")
    private Integer iSRegistered;

    @ApiModelProperty(value = "装潢信息 1-是 2-否", example = "1")
    private Integer isDecoration;

    @ApiModelProperty(value = "其他信息 1-是 2-否", example = "1")
    private Integer isOther;

    @ApiModelProperty(value = "使用年限", example = "5年")
    private Integer usageAgeLimit;

    @ApiModelProperty(value = "折旧年限", example = "10年")
    private Integer depreciationAgeLimit;

    @ApiModelProperty(value = "裸车价", example = "200000.00")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "购置税", example = "20000.00")
    private BigDecimal purchaseTax;

    @ApiModelProperty(value = "牌照费", example = "500.00")
    private BigDecimal licensePlatePrice;

    @ApiModelProperty(value = "上牌杂费", example = "1000.00")
    private BigDecimal licensePlateOtherPrice;

    @ApiModelProperty(value = "装潢费", example = "5000.00")
    private BigDecimal upholsterPrice;

    @ApiModelProperty(value = "购置总价", example = "226500.00")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "操作人", example = "李成祥")
    private String createOperName;

    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "转固申请编号", example = "TF001")
    private String applyNo;

    @ApiModelProperty(value = "转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)", example = "3")
    private Integer applyStatus;

    @ApiModelProperty(value = "资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付）  4-待报废（未交付） 5-已处置 6-已报废")
    private Integer propertyStatus;

}