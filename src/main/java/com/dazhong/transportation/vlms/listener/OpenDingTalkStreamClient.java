package com.dazhong.transportation.vlms.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dazhong.transportation.vlms.config.DingTalkConfig;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.dto.DingTalkFlowNotifyDto;
import com.dazhong.transportation.vlms.thread.DealDingTalkFlowResponseRunnable;
import com.dazhong.transportation.vlms.utils.DingTalkCommentUtils;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import com.dingtalk.open.app.api.GenericEventListener;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.message.GenericOpenDingTalkEvent;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import com.dingtalk.open.app.stream.protocol.event.EventAckStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
@Slf4j
@Profile({"prd","sit"})
public class OpenDingTalkStreamClient {

    @Resource
    DingTalkConfig dingTalkConfig;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private RedisUtils redisUtils;

    private Integer EVENT_EXPIRE_TIME = 3600;

    @PostConstruct
    public void startListener(){
        try {
            OpenDingTalkStreamClientBuilder
                    .custom()
                    .credential(new AuthClientCredential(dingTalkConfig.getDingTalkAppKey(), dingTalkConfig.getDingTalkAppSecret()))
                    //注册事件监听
                    .registerAllEventListener(new GenericEventListener() {
                        public EventAckStatus onEvent(GenericOpenDingTalkEvent event) {
                            //事件唯一Id
                            String eventId = event.getEventId();
                            try {
                                if (Global.instance.redisUtil.exist(eventId)){
                                    return EventAckStatus.SUCCESS;
                                }
                                Global.instance.redisUtil.set(eventId,eventId,EVENT_EXPIRE_TIME);
                                //获取事件体
                                JSONObject bizData = event.getData();
                                //处理事件
                                taskExecutor.execute(new DealDingTalkFlowResponseRunnable(DingTalkCommentUtils.buildNotifyDto(bizData,eventId)));
                                //消费成功
                                return EventAckStatus.SUCCESS;
                            } catch (Exception e) {
                                Global.instance.redisUtil.del(eventId);
                                //消费失败
                                return EventAckStatus.LATER;
                            }
                        }
                    }).build().start();
        }catch (Exception e){
            log.error("DingTalkStreamClient exception ",e);
        }
    }


}
