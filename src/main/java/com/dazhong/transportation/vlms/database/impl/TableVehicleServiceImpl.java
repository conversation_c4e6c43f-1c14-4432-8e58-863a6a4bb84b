package com.dazhong.transportation.vlms.database.impl;

import static com.dazhong.transportation.vlms.mapper.VehicleDepreciationDataDynamicSqlSupport.vehicleDepreciationData;
import static com.dazhong.transportation.vlms.mapper.VehicleInfoDynamicSqlSupport.vehicleInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleManagementLegacyInfoDynamicSqlSupport.vehicleManagementLegacyInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleModelInfoDynamicSqlSupport.vehicleModelInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleOrderReceiptDynamicSqlSupport.vehicleOrderReceipt;
import static com.dazhong.transportation.vlms.mapper.VehiclePurchaseApplyDynamicSqlSupport.vehiclePurchaseApply;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.or;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.sortColumn;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.where.WhereApplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.dazhong.transportation.vlms.database.TableVehicleService;
import com.dazhong.transportation.vlms.dto.ExportAssetVehicleInfoDto;
import com.dazhong.transportation.vlms.dto.SyncAssetVehicleInfoDto;
import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleAssetDto;
import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchAssetVehicleRequest;
import com.dazhong.transportation.vlms.dto.request.SearchAssetVehicleSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchDatabaseTableSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchInboundVehicleRequest;
import com.dazhong.transportation.vlms.dto.response.InboundVehicleResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleListResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleTransferFixedResponse;
import com.dazhong.transportation.vlms.enums.PropertyStatusEnum;
import com.dazhong.transportation.vlms.mapper.VehicleInfoMapper;
import com.dazhong.transportation.vlms.mapper.extend.VehicleInfoExtendMapper;
import com.dazhong.transportation.vlms.model.VehicleInfo;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;


@Service
public class TableVehicleServiceImpl implements TableVehicleService {

    @Autowired
    private VehicleInfoMapper vehicleInfoMapper;

    @Autowired
    private VehicleInfoExtendMapper vehicleInfoExtendMapper;


    @Override
    public List<InboundVehicleResponse> searchInboundVehicleList(SearchInboundVehicleRequest request, TokenUserInfo tokenUserInfo) {
        List<Long> orgIdList = tokenUserInfo.getOrgIdList();
        List<Integer> ownerIdList = tokenUserInfo.getOwnerIdList();
        SelectStatementProvider provider = select(
                vehicleInfo.vin,
                vehicleInfo.licensePlate,
                vehicleInfo.vehicleAssetId,
                vehicleInfo.engineNo,
                vehicleInfo.engineModel,
                vehicleInfo.vehicleColorId,
                vehicleInfo.interiorColor,
                vehicleInfo.supplierId,
                vehicleInfo.isRepurchase,
                vehicleInfo.repurchaseDate,
                vehicleInfo.repurchaseRequirements,
                vehicleModelInfo.vehicleTypeId,
                vehicleModelInfo.vehicleModelName,
                vehicleModelInfo.vehicleModelNo,
                vehicleOrderReceipt.orderDate,
                vehicleOrderReceipt.receiptDate,
                vehicleOrderReceipt.createOperName,
                vehicleOrderReceipt.createTime
        )
                .from(vehicleInfo,"t1")
                .leftJoin(vehicleOrderReceipt).on(vehicleInfo.vin, equalTo(vehicleOrderReceipt.vin))
                .leftJoin(vehicleModelInfo).on(vehicleInfo.vehicleModelId, equalTo(vehicleModelInfo.id))
                .where()
                .and(vehicleInfo.propertyStatus, isEqualTo(0))
                .and(vehicleInfo.vin, isLikeWhenPresent(transFuzzyQueryParam(request.getVin())))
                .and(vehicleInfo.licensePlate, isLikeWhenPresent(transFuzzyQueryParam(request.getLicensePlate())))
                .and(vehicleInfo.subscriptionCompanyCode, isEqualToWhenPresent(request.getSubscriptionCompanyCode()).filter(ObjectValidUtil::isValid))
                // 登录用户关联ownerIdList
                .and(vehicleInfo.assetCompanyId, isInWhenPresent(ownerIdList))
                // 登录用户关联orgIdList
//                .and(vehicleInfo.ownOrganizationId, isInWhenPresent(orgIdList),or(vehicleInfo.usageOrganizationId, isInWhenPresent(orgIdList)))
                .orderBy(sortColumn("t1.update_time").descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<InboundVehicleResponse> vehicleInfoExtendList = vehicleInfoExtendMapper.searchInboundVehicleList(provider);
        return vehicleInfoExtendList;
    }

    @Override
    public List<VehicleListResponse> searchAssetVehicleList(SearchAssetVehicleRequest request, TokenUserInfo tokenUserInfo) {
        List<Long> orgIdList = tokenUserInfo.getOrgIdList();
        List<Integer> ownerIdList = tokenUserInfo.getOwnerIdList();
        SelectStatementProvider provider = select(
                vehicleInfo.vin,
                vehicleInfo.licensePlate,
                vehicleInfo.vehicleAssetId,
                vehicleInfo.vehicleModelId,
                vehicleInfo.vehicleColorId,
                vehicleInfo.productLine,
                vehicleInfo.businessLine,
                vehicleInfo.propertyStatus,
                vehicleInfo.operatingStatus,
                vehicleInfo.obtainWayId,
                vehicleInfo.belongingTeam,
                vehicleInfo.assetCompanyId,
                vehicleInfo.ownOrganizationId,
                vehicleInfo.usageOrganizationId,
                vehicleInfo.annualInspectionDueDateRegistrationCard,
                vehicleInfo.registrationDateRegistrationCard,
                vehicleModelInfo.vehicleModelName,
                vehicleModelInfo.vehicleAbbreviationId,
                vehicleModelInfo.assessPassenger
        )
                .from(vehicleInfo,"t1")
                .leftJoin(vehicleModelInfo).on(vehicleInfo.vehicleModelId, equalTo(vehicleModelInfo.id))
                .where()
                // 登录用户关联ownerIdList
                .and(vehicleInfo.assetCompanyId, isInWhenPresent(ownerIdList))
                // 登录用户关联orgIdList
                .and(vehicleInfo.ownOrganizationId, isInWhenPresent(orgIdList),or(vehicleInfo.usageOrganizationId, isInWhenPresent(orgIdList)))
                //车架号（模糊查询）
                .and(vehicleInfo.vin, isLikeWhenPresent(transFuzzyQueryParam(request.getVin())))
                //车牌号（模糊查询）
                .and(vehicleInfo.licensePlate, isLikeWhenPresent(transFuzzyQueryParam(request.getLicensePlate())))
                //资产所属公司
                .and(vehicleInfo.assetCompanyId, isInWhenPresent(request.getAssetCompanyIdList()))
                //实际运营公司（所属）
                .and(vehicleInfo.ownOrganizationId, isInWhenPresent(request.getOwnOrganizationIdList()))
                //实际运营公司（使用）
                .and(vehicleInfo.usageOrganizationId, isInWhenPresent(request.getUsageOrganizationIdList()))
                //条线
                .and(vehicleInfo.productLine, isEqualToWhenPresent(request.getProductLine()))
                //业务类型
                .and(vehicleInfo.businessLine, isInWhenPresent(request.getBusinessLine()))
                //资产状态
                .and(vehicleInfo.propertyStatus, isInWhenPresent(request.getPropertyStatus()))
                //运营状态
                .and(vehicleInfo.operatingStatus, isInWhenPresent(request.getOperatingStatus()))
                //车架号列表批量查询
                .and(vehicleInfo.vin, isInWhenPresent(request.getVinList()))
                .orderBy(sortColumn("t1.update_time").descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoExtendMapper.searchAssetVehicleList(provider);
    }

    @Override
    public List<VehicleListResponse> queryAssetVehicleList(List<String> vinList) {
        SelectStatementProvider provider = select(
                vehicleInfo.vin,
                vehicleInfo.licensePlate,
                vehicleInfo.vehicleAssetId,
                vehicleInfo.productLine,
                vehicleInfo.businessLine,
                vehicleInfo.propertyStatus,
                vehicleInfo.operatingStatus,
                vehicleInfo.obtainWayId,
                vehicleInfo.belongingTeam,
                vehicleInfo.assetCompanyId,
                vehicleInfo.ownOrganizationId,
                vehicleInfo.usageOrganizationId,
                vehicleInfo.annualInspectionDueDateRegistrationCard,
                vehicleInfo.registrationDateRegistrationCard,
                vehicleModelInfo.vehicleModelName,
                vehicleOrderReceipt.receiptDate
        )
                .from(vehicleInfo)
                .leftJoin(vehicleOrderReceipt).on(vehicleInfo.vin, equalTo(vehicleOrderReceipt.vin))
                .leftJoin(vehicleModelInfo).on(vehicleInfo.vehicleModelId, equalTo(vehicleModelInfo.id))
                .where()
                .and(vehicleInfo.vin, isInWhenPresent(vinList))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoExtendMapper.queryAssetVehicleList(provider);
    }

    @Override
    public List<VehicleListResponse> queryVehicleList(List<String> vinList) {
        SelectStatementProvider provider = select(
                vehicleInfo.vin,
                vehicleInfo.licensePlate,
                vehicleInfo.vehicleAssetId,
                vehicleInfo.productLine,
                vehicleInfo.businessLine,
                vehicleInfo.propertyStatus,
                vehicleInfo.operatingStatus,
                vehicleInfo.obtainWayId,
                vehicleInfo.belongingTeam,
                vehicleInfo.assetCompanyId,
                vehicleInfo.ownOrganizationId,
                vehicleInfo.usageOrganizationId,
                vehicleInfo.annualInspectionDueDateRegistrationCard,
                vehicleInfo.registrationDateRegistrationCard,
                vehicleInfo.vehicleModelId,
                vehicleModelInfo.vehicleModelName)
                .from(vehicleInfo)
                .leftJoin(vehicleModelInfo).on(vehicleInfo.vehicleModelId, equalTo(vehicleModelInfo.id))
                .where()
                .and(vehicleInfo.vin, isInWhenPresent(vinList))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoExtendMapper.queryAssetVehicleList(provider);
    }

    @Override
    public List<VehicleTransferFixedResponse> searchVehicleTransferFixed(List<String> vinList) {
        SelectStatementProvider provider = select(
                vehicleInfo.vin,
                vehicleInfo.licensePlate,
                vehicleInfo.vehicleAssetId,
                vehicleInfo.productLine,
                vehicleInfo.businessLine,
                vehicleInfo.usageAgeLimit,
                vehicleInfo.depreciationAgeLimit,
                vehicleInfo.assetCompanyId,
                vehicleInfo.ownOrganizationId,
                vehicleInfo.usageOrganizationId,
                vehicleInfo.purchasePrice,
                vehicleInfo.purchaseTax,
                vehicleInfo.licensePlatePrice,
                vehicleInfo.licensePlateOtherPrice,
                vehicleInfo.upholsterPrice,
                vehicleInfo.totalPrice,
                vehicleInfo.propertyStatus,
                vehicleModelInfo.vehicleModelName,
                vehicleOrderReceipt.receiptDate,
                vehiclePurchaseApply.approvalNumber
        )
                .from(vehicleInfo)
                .leftJoin(vehicleOrderReceipt).on(vehicleInfo.vin, equalTo(vehicleOrderReceipt.vin))
                .leftJoin(vehiclePurchaseApply).on(vehicleOrderReceipt.purchaseApplyId, equalTo(vehiclePurchaseApply.id))
                .leftJoin(vehicleModelInfo).on(vehicleInfo.vehicleModelId, equalTo(vehicleModelInfo.id))
                .where()
                .and(vehicleInfo.isDeleted, isEqualTo(0))
                .and(vehicleInfo.vin, isIn(vinList))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoExtendMapper.searchVehicleTransferFixed(provider);
    }

    @Override
    public VehicleInfo queryVehicleByVin(String vin) {
        SelectStatementProvider selectStatement = select(vehicleInfo.allColumns())
                .from(vehicleInfo)
                .where()
                .and(vehicleInfo.vin, isEqualTo(vin))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehicleInfo> optional = vehicleInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public VehicleInfo queryVehicleByLicensePlate(String licensePlate) {
        SelectStatementProvider selectStatement = select(vehicleInfo.allColumns())
                .from(vehicleInfo)
                .where()
                .and(vehicleInfo.licensePlate, isEqualTo(licensePlate))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehicleInfo> optional = vehicleInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public List<VehicleInfo> queryVehicleListByVin(String vin, String licensePlate) {
        if (StrUtil.isBlank(vin) && StrUtil.isBlank(licensePlate)) {
            return Collections.emptyList();
        }
        if (StrUtil.isNotBlank(vin)) {
            vin = StrUtil.format("%{}%", vin);
        }
        if (StrUtil.isNotBlank(licensePlate)) {
            licensePlate = StrUtil.format("%{}%", licensePlate);
        }
        SelectStatementProvider provider = select(
                vehicleInfo.allColumns()
        )
                .from(vehicleInfo)
                .where()
                .and(vehicleInfo.vin, isLikeWhenPresent(vin).filter(StringUtils::isNotBlank))
                .and(vehicleInfo.licensePlate, isLikeWhenPresent(licensePlate).filter(StringUtils::isNotBlank))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.selectMany(provider);
    }

    @Override
    public List<VehicleInfo> queryVehicleByVinList(List<String> vinList) {
        if (CollectionUtil.isEmpty(vinList)) {
            return Collections.emptyList();
        }
        SelectStatementProvider selectStatement = select(vehicleInfo.allColumns())
                .from(vehicleInfo)
                .where()
                .and(vehicleInfo.vin, isIn(vinList))
                .and(vehicleInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.selectMany(selectStatement);
    }

    @Override
    public List<VehicleInfo> queryVehicleByLicensePlateList(List<String> licensePlateList) {
        if (CollectionUtil.isEmpty(licensePlateList)) {
            return Collections.emptyList();
        }
        SelectStatementProvider selectStatement = select(vehicleInfo.allColumns())
                .from(vehicleInfo)
                .where()
                .and(vehicleInfo.licensePlate, isIn(licensePlateList))
                .and(vehicleInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.selectMany(selectStatement);
    }

    @Override
    public List<VehicleInfo> queryVehicleByAssetIdList(List<String> vehicleAssetIdList) {
        if (CollectionUtil.isEmpty(vehicleAssetIdList)) {
            return Collections.emptyList();
        }
        SelectStatementProvider selectStatement = select(vehicleInfo.allColumns())
                .from(vehicleInfo)
                .where()
                .and(vehicleInfo.vehicleAssetId, isIn(vehicleAssetIdList))
                .and(vehicleInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.selectMany(selectStatement);
    }

    @Override
    public int saveVehicle(VehicleInfo vehicleInfo, TokenUserInfo tokenUserInfo) {
        vehicleInfo.setCreateOperId(tokenUserInfo.getUserId());
        vehicleInfo.setCreateOperName(tokenUserInfo.getName());
        return vehicleInfoMapper.insertSelective(vehicleInfo);
    }

    @Override
    public int updateVehicle(VehicleInfo vehicleInfo, TokenUserInfo tokenUserInfo) {
        if (vehicleInfo != null) {
            vehicleInfo.setUpdateOperId(tokenUserInfo.getUserId());
            vehicleInfo.setUpdateOperName(tokenUserInfo.getName());
        }
        vehicleInfo.setUpdateTime(new Date());
        return vehicleInfoMapper.updateByPrimaryKeySelective(vehicleInfo);
    }

    @Override
    public int updateLicensePlateByVin(VehicleInfo updateVehicleInfo, TokenUserInfo tokenUserInfo) {
        UpdateStatementProvider updateStatementProvider = SqlBuilder.update(vehicleInfo)
                .set(vehicleInfo.updateOperId).equalTo(tokenUserInfo.getUserId())
                .set(vehicleInfo.updateOperName).equalTo(tokenUserInfo.getName())
                .set(vehicleInfo.licensePlate).equalToWhenPresent(updateVehicleInfo.getLicensePlate())
                .set(vehicleInfo.vehicleTypeRegistrationCard).equalToWhenPresent(updateVehicleInfo.getVehicleTypeRegistrationCard())
                .set(vehicleInfo.usageIdRegistrationCard).equalToWhenPresent(updateVehicleInfo.getUsageIdRegistrationCard())
                .set(vehicleInfo.registrationDateRegistrationCard).equalToWhenPresent(updateVehicleInfo.getRegistrationDateRegistrationCard())
                .set(vehicleInfo.issuanceDateRegistrationCard).equalToWhenPresent(updateVehicleInfo.getIssuanceDateRegistrationCard())
                .set(vehicleInfo.fileNumber).equalToWhenPresent(updateVehicleInfo.getFileNumber())
                .set(vehicleInfo.retirementDateRegistrationCard).equalToWhenPresent(updateVehicleInfo.getRetirementDateRegistrationCard())
                .set(vehicleInfo.annualInspectionDueDateRegistrationCard).equalToWhenPresent(updateVehicleInfo.getAnnualInspectionDueDateRegistrationCard())
                .set(vehicleInfo.quotaType).equalToWhenPresent(updateVehicleInfo.getQuotaType())
                .set(vehicleInfo.quotaAssetCompanyId).equalToWhenPresent(updateVehicleInfo.getQuotaAssetCompanyId())
                .set(vehicleInfo.useQuotaType).equalToWhenPresent(updateVehicleInfo.getUseQuotaType())
                .where(vehicleInfo.vin, isEqualTo(updateVehicleInfo.getVin()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.update(updateStatementProvider);
    }

    @Override
    public int batchUpdateVehicleBelongingTeam(List<String> vinList, String belongingTeam) {
        UpdateStatementProvider updateStatementProvider = SqlBuilder.update(vehicleInfo)
                .set(vehicleInfo.belongingTeam).equalToWhenPresent(belongingTeam)
                .where(vehicleInfo.vin, isInWhenPresent(vinList))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.update(updateStatementProvider);
    }

    @Override
    public int batchUpdateVehicleOperatingStatus(List<String> vinList, Integer operatingStatus) {
        UpdateStatementProvider updateStatementProvider = SqlBuilder.update(vehicleInfo)
                .set(vehicleInfo.operatingStatus).equalToWhenPresent(operatingStatus)
                .where(vehicleInfo.vin, isInWhenPresent(vinList))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.update(updateStatementProvider);
    }

    @Override
    public int updateVehicleByVinSelective(VehicleInfo updateVehicleInfo) {
        UpdateStatementProvider updateStatementProvider = SqlBuilder.update(vehicleInfo)
                .set(vehicleInfo.usageOrganizationId).equalToWhenPresent(updateVehicleInfo.getUsageOrganizationId())
                .set(vehicleInfo.ownOrganizationId).equalToWhenPresent(updateVehicleInfo.getOwnOrganizationId())
                .set(vehicleInfo.assetCompanyId).equalToWhenPresent(updateVehicleInfo.getAssetCompanyId())
                .set(vehicleInfo.productLine).equalToWhenPresent(updateVehicleInfo.getProductLine())
                .set(vehicleInfo.businessLine).equalToWhenPresent(updateVehicleInfo.getBusinessLine())
                .set(vehicleInfo.propertyStatus).equalToWhenPresent(updateVehicleInfo.getPropertyStatus())
                .set(vehicleInfo.realRetirementDate).equalToWhenPresent(updateVehicleInfo.getRealRetirementDate())
                .where(vehicleInfo.vin, isEqualTo(updateVehicleInfo.getVin()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.update(updateStatementProvider);
    }

    @Override
    public List<SyncAssetVehicleInfoDto> searchAssetVehicleSyncInfo(SearchAssetVehicleSyncDataRequest request){
        SelectStatementProvider selectStatement = select(
                vehicleInfo.id,
                vehicleInfo.licensePlate,
                vehicleInfo.usageIdRegistrationCard,
                vehicleInfo.vehicleColorId,
                vehicleInfo.productDate,
                vehicleInfo.totalPrice,
                vehicleInfo.remainPrice,
                vehicleInfo.secondHandPrice,
                vehicleInfo.upholsterPrice,
                vehicleInfo.licensePlateOtherPrice,
                vehicleInfo.purchasePrice,
                vehicleInfo.purchaseTax,
                vehicleInfo.engineNo,
                vehicleInfo.vin,
                vehicleInfo.realRetirementDate,
                vehicleInfo.usageOrganizationId,
                vehicleInfo.vehicleModelId,
                //车型信息
                vehicleModelInfo.vehicleAbbreviationId,
                vehicleModelInfo.vehicleModelName,
                vehicleModelInfo.financialModelName,
                vehicleModelInfo.assessPassenger,
                vehicleModelInfo.manufacturerId,
                vehicleModelInfo.wheelQuantity,
                vehicleModelInfo.wheelParam,
                vehicleModelInfo.outLength,
                vehicleModelInfo.outWidth,
                vehicleModelInfo.outHeight,
                vehicleModelInfo.gasTypeId,
                vehicleModelInfo.wheelBase1,
                vehicleModelInfo.wheelBase2,
                vehicleModelInfo.assessMass,
                vehicleModelInfo.totalMass,
                vehicleModelInfo.vehicleTypeId,
                //旧系统数据
                vehicleManagementLegacyInfo.licenseDate,
                vehicleManagementLegacyInfo.ownerId,
                vehicleManagementLegacyInfo.startDate
        )
                .from(vehicleInfo)
                .leftJoin(vehicleModelInfo).on(vehicleInfo.vehicleModelId, equalTo(vehicleModelInfo.id))
                .leftJoin(vehicleManagementLegacyInfo).on(vehicleInfo.vin, equalTo(vehicleManagementLegacyInfo.vin))
                .where()
                .and(vehicleInfo.licensePlate, isEqualToWhenPresent(request.getCarNo()).filter(StringUtils::isNotBlank))
                .and(vehicleInfo.licensePlate, isLikeWhenPresent(transFuzzyQueryParam(request.getCarNoLike())))
                .and(vehicleInfo.usageOrganizationId, isInWhenPresent(request.getOrgIdList()))
                .orderBy(vehicleInfo.id)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoExtendMapper.searchAssetVehicleSyncData(selectStatement);

    }

    @Override
    public List<SyncDatabaseVehicleDto> searchVehicleDatabaseSyncInfo(SearchDatabaseTableSyncDataRequest request) {
        SelectStatementProvider selectStatement = select(
                vehicleInfo.id,
                vehicleInfo.licensePlate,
                vehicleInfo.vin,
                vehicleInfo.engineNo,
                vehicleInfo.productDate,
                vehicleInfo.repurchaseDate,
                vehicleInfo.realRetirementDate,
                vehicleInfo.usageAgeLimit,
                vehicleInfo.depreciationAgeLimit,
                vehicleInfo.obtainWayId,
                vehicleInfo.usageIdRegistrationCard,
                vehicleInfo.vehicleColorId,
                vehicleInfo.areaId,
                vehicleInfo.supplierId,
                vehicleInfo.vehicleModelId,
                vehicleInfo.certificateNumber,
                vehicleInfo.retirementDateRegistrationCard,
                vehicleInfo.ownOrganizationId,
                vehicleInfo.usageOrganizationId,
                vehicleInfo.propertyStatus,
                vehicleInfo.relateAssetId,
                vehicleInfo.vehicleAssetId,

                vehicleManagementLegacyInfo.startDate,
                vehicleManagementLegacyInfo.licenseDate,
                vehicleManagementLegacyInfo.operatingNo,
                vehicleManagementLegacyInfo.ownerId,
                vehicleManagementLegacyInfo.assetOwnerId,
                vehicleManagementLegacyInfo.vehicleCategoryId,
                vehicleManagementLegacyInfo.operateTypeId,
                vehicleManagementLegacyInfo.contractTypeId,
                vehicleManagementLegacyInfo.operationCategoryId,
                vehicleManagementLegacyInfo.operationStartDate,
                vehicleManagementLegacyInfo.companyOwnerId,
                vehicleManagementLegacyInfo.hasRight,
                vehicleManagementLegacyInfo.fromCompany
        )
                .from(vehicleInfo)
                .leftJoin(vehicleManagementLegacyInfo).on(vehicleInfo.vin, equalTo(vehicleManagementLegacyInfo.vin))
                .where()
                .and(vehicleInfo.licensePlate, isEqualToWhenPresent(request.getCarNo()).filter(StringUtils::isNotBlank))
                .and(vehicleInfo.licensePlate, isLikeWhenPresent(transFuzzyQueryParam(request.getCarNoLike())))
                .and(vehicleInfo.usageOrganizationId, isInWhenPresent(request.getOrgIdList()))
                .and(vehicleInfo.id, isGreaterThanWhenPresent(request.getIndex()))
                .orderBy(vehicleInfo.id)
                .limit(request.getPageSize())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<SyncDatabaseVehicleDto> list = vehicleInfoExtendMapper.searchVehicleDatabaseSyncData(selectStatement);
        //对特殊字段进行处理
        for (SyncDatabaseVehicleDto syncDatabaseVehicleDto : list) {
            //对关联车辆资产表进行处理
            if(syncDatabaseVehicleDto.getVehicleAssetId() == null){
                syncDatabaseVehicleDto.setVehicleAssetId(syncDatabaseVehicleDto.getId());
            }
            //对车辆状态进行处理
            if(Objects.equals(syncDatabaseVehicleDto.getPropertyStatus(),PropertyStatusEnum.FIXED_ASSET.getCode())){
                //vehicleStatus 1:在运 0:退役
                syncDatabaseVehicleDto.setVehicleStatus(1);
            }else{
                syncDatabaseVehicleDto.setVehicleStatus(0);
            }

        }
        return list;
    }

    @Override
    public List<SyncDatabaseVehicleAssetDto> searchVehicleAssetDatabaseSyncInfo(SearchDatabaseTableSyncDataRequest request) {
       SelectStatementProvider selectStatement = select(
               vehicleInfo.id,
               vehicleInfo.relateAssetId,
               vehicleInfo.purchasePrice,
               vehicleInfo.purchaseTax,
               vehicleInfo.licensePlatePrice,
               vehicleInfo.licensePlateOtherPrice,
               vehicleInfo.upholsterPrice,
               vehicleInfo.totalPrice,
               vehicleInfo.remainPrice,
               vehicleInfo.secondHandPrice
       )
               .from(vehicleInfo)
               .where()
               .and(vehicleInfo.id, isGreaterThanWhenPresent(request.getIndex()))
               .and(vehicleInfo.licensePlate, isEqualToWhenPresent(request.getCarNo()).filter(StringUtils::isNotBlank))
               .and(vehicleInfo.licensePlate, isLikeWhenPresent(transFuzzyQueryParam(request.getCarNoLike())))
               .and(vehicleInfo.usageOrganizationId, isInWhenPresent(request.getOrgIdList()))

               .orderBy(vehicleInfo.id)
               .limit(request.getPageSize())
               .build()
               .render(RenderingStrategies.MYBATIS3);
        List<SyncDatabaseVehicleAssetDto> list = vehicleInfoExtendMapper.searchVehicleAssetDatabaseSyncData(selectStatement);
        //对特殊字段进行处理
        for (SyncDatabaseVehicleAssetDto syncDatabaseVehicleAssetDto : list) {
            if(syncDatabaseVehicleAssetDto.getId() == null){
                syncDatabaseVehicleAssetDto.setId(syncDatabaseVehicleAssetDto.getVehicleId());
            }
        }
        return list;
    }

    @Override
    public List<ExportAssetVehicleInfoDto> exportAssetVehicleInfo(Long index, SearchAssetVehicleRequest request, TokenUserInfo tokenUserInfo) {
        List<Long> orgIdList = tokenUserInfo.getOrgIdList();
        List<Integer> ownerIdList = tokenUserInfo.getOwnerIdList();

        //具体字段含义，可以参考ExportAssetVehicleInfoDto类
        SelectStatementProvider selectStatement = select(
                //车辆基础数据
                vehicleInfo.id,
                vehicleInfo.vin,
                vehicleInfo.engineNo,
                vehicleInfo.engineModel,
                vehicleInfo.licensePlate,
                vehicleInfo.quotaType,
                vehicleInfo.quotaAssetCompanyId,
                vehicleInfo.propertyStatus,
                vehicleInfo.usageAgeLimit,
                vehicleInfo.remainPrice,
                vehicleInfo.assetCompanyId,
                vehicleInfo.ownOrganizationId,
                vehicleInfo.usageOrganizationId,
                vehicleInfo.belongingTeam,
                vehicleInfo.productLine,
                vehicleInfo.businessLine,
                vehicleInfo.operatingStatus,
                vehicleInfo.usageIdRegistrationCard,
                vehicleInfo.vehicleTypeRegistrationCard,
                vehicleInfo.retirementDateRegistrationCard,
                vehicleInfo.realRetirementDate,
                //旧系统数据
                vehicleManagementLegacyInfo.operateTypeId,
                vehicleManagementLegacyInfo.operatingNo,
                vehicleManagementLegacyInfo.startDate,
                vehicleManagementLegacyInfo.licenseDate,
                //车型信息
                vehicleModelInfo.vehicleModelNo,
                vehicleModelInfo.vehicleModelName,
                vehicleModelInfo.financialModelName,
                vehicleModelInfo.vehicleAbbreviationId,
                vehicleModelInfo.assessPassenger,
                vehicleModelInfo.busAssessPassenger,
                vehicleModelInfo.gasTypeId,
                vehicleModelInfo.wheelParam,
                vehicleModelInfo.exhaustId,
                vehicleModelInfo.outLength,
                vehicleModelInfo.totalMass,
                vehicleModelInfo.fuelTankCapacity,
                vehicleModelInfo.batteryCapacity,
                vehicleModelInfo.engineModelNo,
                //折旧信息
                vehicleDepreciationData.originalAmount,
                vehicleDepreciationData.depreciationMonths,
                vehicleDepreciationData.depreciationStartDate,
                vehicleDepreciationData.currentMonth,
                vehicleDepreciationData.accumulatedDepreciationMonths,
                vehicleDepreciationData.depreciationAmount,
                vehicleDepreciationData.remainingResidualValue
        )
                .from(vehicleInfo)
                .leftJoin(vehicleModelInfo).on(vehicleInfo.vehicleModelId, equalTo(vehicleModelInfo.id))
                .leftJoin(vehicleManagementLegacyInfo).on(vehicleInfo.vin, equalTo(vehicleManagementLegacyInfo.vin))
                .leftJoin(vehicleDepreciationData).on(vehicleInfo.depreciationDataId, equalTo(vehicleDepreciationData.id))
                .where()
                // 登录用户关联ownerIdList
                .and(vehicleInfo.assetCompanyId, isInWhenPresent(ownerIdList))
                // 登录用户关联orgIdList
                .and(vehicleInfo.ownOrganizationId, isInWhenPresent(orgIdList),or(vehicleInfo.usageOrganizationId, isInWhenPresent(orgIdList)))
                //车架号（模糊查询）
                .and(vehicleInfo.vin, isLikeWhenPresent(transFuzzyQueryParam(request.getVin())))
                //车牌号（模糊查询）
                .and(vehicleInfo.licensePlate, isLikeWhenPresent(transFuzzyQueryParam(request.getLicensePlate())))
                //资产所属公司
                .and(vehicleInfo.assetCompanyId, isInWhenPresent(request.getAssetCompanyIdList()))
                //实际运营公司（所属）
                .and(vehicleInfo.ownOrganizationId, isInWhenPresent(request.getOwnOrganizationIdList()))
                //实际运营公司（使用）
                .and(vehicleInfo.usageOrganizationId, isInWhenPresent(request.getUsageOrganizationIdList()))
                //条线
                .and(vehicleInfo.productLine, isEqualToWhenPresent(request.getProductLine()))
                //业务类型
                .and(vehicleInfo.businessLine, isInWhenPresent(request.getBusinessLine()))
                //资产状态
                .and(vehicleInfo.propertyStatus, isInWhenPresent(request.getPropertyStatus()))
                //根据ID排序
                .and(vehicleInfo.id, isGreaterThanWhenPresent(index))
                //运营状态
                .and(vehicleInfo.operatingStatus, isInWhenPresent(request.getOperatingStatus()))
                .orderBy(vehicleInfo.id)
                .limit(1000)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return vehicleInfoExtendMapper.exportAssetVehicleInfo(selectStatement);
    }

    @Override
    public List<VehicleInfo> selectAllUsageOrganizationId() {
        SelectStatementProvider selectStatement = select(
                vehicleInfo.vin,
                vehicleInfo.usageOrganizationId)
                .from(vehicleInfo)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.selectMany(selectStatement);
    }

    @Override
    public List<VehicleInfo> selectSyncSaasVehicleInfo(Long lastId, Integer pageSize, boolean isFullSync) {
        // 获取当天0点的时间
        LocalDateTime todayStart = LocalDate.now().atStartOfDay();
        Date todayStartDate = Date.from(todayStart.atZone(ZoneId.systemDefault()).toInstant());

        // 构建查询条件
        WhereApplier whereApplier = isFullSync
                ? c -> c.where(vehicleInfo.id, isGreaterThan(lastId)).and(vehicleInfo.licensePlate, isNotEqualTo(""))
                : c -> c.where(vehicleInfo.id, isGreaterThan(lastId))
                .and(vehicleInfo.licensePlate, isNotEqualTo(""))
                .and(vehicleInfo.updateTime, isGreaterThanOrEqualTo(todayStartDate));
        
        SelectStatementProvider selectStatement = select(
                vehicleInfo.id,
                vehicleInfo.vin,
                vehicleInfo.licensePlate,
                vehicleInfo.vehicleModelId,
                vehicleInfo.ownOrganizationId,
                vehicleInfo.usageOrganizationId,
                vehicleInfo.productLine,
                vehicleInfo.businessLine,
                vehicleInfo.engineNo,
                vehicleInfo.registrationDateRegistrationCard,
                vehicleInfo.latestTotalMileage,
                vehicleInfo.propertyStatus)
                .from(vehicleInfo)
                .applyWhere(whereApplier)
                .orderBy(vehicleInfo.id)
                .limit(pageSize)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.selectMany(selectStatement);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int updateVehicleOperatingStatusInNewTransaction(String vin, Integer operatingStatus) {
        UpdateStatementProvider updateStatementProvider = SqlBuilder.update(vehicleInfo)
                .set(vehicleInfo.operatingStatus).equalToWhenPresent(operatingStatus)
                .where(vehicleInfo.vin, isEqualTo(vin))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleInfoMapper.update(updateStatementProvider);
    }
}
