package com.dazhong.transportation.vlms.database.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.dazhong.transportation.vlms.database.TableVehicleManagementLegacyService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.VehicleManagementLegacyInfoMapper;
import com.dazhong.transportation.vlms.model.VehicleManagementLegacyInfo;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehicleManagementLegacyInfoDynamicSqlSupport.vehicleManagementLegacyInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;


@Service
public class TableVehicleManagementLegacyServiceImpl implements TableVehicleManagementLegacyService {

    @Autowired
    private VehicleManagementLegacyInfoMapper vehicleManagementLegacyInfoMapper;


    @Override
    public VehicleManagementLegacyInfo queryVehicleByVin(String vin) {
        SelectStatementProvider selectStatement = select(vehicleManagementLegacyInfo.allColumns())
                .from(vehicleManagementLegacyInfo)
                .where()
                .and(vehicleManagementLegacyInfo.vin, isEqualTo(vin))
                .and(vehicleManagementLegacyInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehicleManagementLegacyInfo> optional = vehicleManagementLegacyInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }


    @Override
    public List<VehicleManagementLegacyInfo> queryVehicleByVinList(List<String> vinList) {
        if (CollectionUtil.isEmpty(vinList)) {
            return Collections.emptyList();
        }
        SelectStatementProvider selectStatement = select(vehicleManagementLegacyInfo.allColumns())
                .from(vehicleManagementLegacyInfo)
                .where()
                .and(vehicleManagementLegacyInfo.vin, isIn(vinList))
                .and(vehicleManagementLegacyInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleManagementLegacyInfoMapper.selectMany(selectStatement);
    }


    @Override
    public VehicleManagementLegacyInfo insert(VehicleManagementLegacyInfo vehicleManagementLegacyInfo) {
        vehicleManagementLegacyInfoMapper.insertSelective(vehicleManagementLegacyInfo);
        return vehicleManagementLegacyInfo;
    }

    @Override
    public VehicleManagementLegacyInfo insert(VehicleManagementLegacyInfo vehicleManagementLegacyInfo, TokenUserInfo tokenUserInfo) {
        vehicleManagementLegacyInfo.setCreateOperId(tokenUserInfo.getUserId());
        vehicleManagementLegacyInfo.setCreateOperName(tokenUserInfo.getName());
        vehicleManagementLegacyInfo.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleManagementLegacyInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.insert(vehicleManagementLegacyInfo);
    }

    @Override
    public int updateSelectiveById(VehicleManagementLegacyInfo vehicleManagementLegacyInfo, TokenUserInfo tokenUserInfo) {
        vehicleManagementLegacyInfo.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleManagementLegacyInfo.setUpdateOperName(tokenUserInfo.getName());
        return vehicleManagementLegacyInfoMapper.updateByPrimaryKeySelective(vehicleManagementLegacyInfo);
    }
}
