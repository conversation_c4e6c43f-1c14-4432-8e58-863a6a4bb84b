package com.dazhong.transportation.vlms.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.TransferFixedApplyResponse;
import com.dazhong.transportation.vlms.dto.response.TransferFixedDetailsResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleTransferFixedResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.excel.ImportTransfer;
import com.dazhong.transportation.vlms.excel.util.ExcelUtil;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.service.ITransferFixedService;
import com.dazhong.transportation.vlms.utils.CommonUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;


@Api(value = "车辆转固相关接口", tags = "车辆转固相关接口")
@Slf4j
@RestController
@RequestMapping(value = "api")
public class TransferFixedController {

    @Autowired
    private ITransferFixedService transferFixedService;

    /**
     * 查询转固申请列表
     *
     * @return
     */
    @ApiOperation(value = "查询转固申请列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "searchTransferFixedApply", method = RequestMethod.POST)
    public ResultResponse<PageResponse<TransferFixedApplyResponse>> searchTransferFixedApply(@RequestBody SearchTransferFixedRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        PageResponse pageResponse = transferFixedService.searchVehicleTransferFixedApply(request, tokenUserInfo);
        return ResultResponse.success(pageResponse);
    }

    /**
     * 创建车辆转固申请
     *
     * @return
     */
    @ApiOperation(value = "创建车辆转固申请", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "addVehicleTransferFixed", method = RequestMethod.POST)
    public ResultResponse addVehicleTransferFixed(@RequestBody AddFixedAssetVehicleRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        if (StringUtils.isBlank(tokenUserInfo.getDingTalkNum()) && request.getOperateType() == 2){
            return ResultResponse.businessFailed("当前用户未绑定钉钉账号，请先绑定钉钉账号");
        }
        return transferFixedService.addVehicleTransferFixed(request, tokenUserInfo);
    }


    /**
     * 编辑车辆转固申请
     *
     * @return
     */
    @ApiOperation(value = "编辑车辆转固申请", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "updateVehicleTransferFixed", method = RequestMethod.POST)
    public ResultResponse updateVehicleTransferFixed(@RequestBody UpdateFixedAssetVehicleRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        if (StringUtils.isBlank(tokenUserInfo.getDingTalkNum()) && request.getOperateType() == 2){
            return ResultResponse.businessFailed("当前用户未绑定钉钉账号，请先绑定钉钉账号");
        }
        return transferFixedService.updateVehicleTransferFixed(request, tokenUserInfo);
    }


    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "修改转固申请状态(撤回和作废)", httpMethod = "POST")
    @RequestMapping(value = "editTransferFixedStatus", method = RequestMethod.POST)
    public ResultResponse editTransferFixedStatus(@RequestBody EditTransferFixedStatusRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return transferFixedService.editTransferFixedStatus(request, tokenUserInfo);
    }

    /**
     * 查询转固申请详情
     *
     * @param applyNo
     * @return
     */
    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "查询转固申请详情", httpMethod = "GET")
    @RequestMapping(value = "queryTransferFixedDetail/{applyNo}", method = RequestMethod.GET)
    public ResultResponse<TransferFixedDetailsResponse> queryTransferFixedDetail(@ApiParam(value = "转固申请编号 必传", required = true) @PathVariable String applyNo) {
        return transferFixedService.queryTransferFixedDetail(applyNo);
    }

    /**
     * 导入车辆信息
     *
     * @return
     */
    @ApiOperation(value = "导入车辆信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "importVehicleTransferFixed", method = RequestMethod.POST)
    public ResultResponse<VehicleTransferFixedResponse> importVehicleTransferFixed(@RequestBody BaseImportFileUrlRequest request) {
        try {
            ValidationUtils.validate(request);
            String filePath = request.getFilePath();
            List<ImportTransfer> readList = ExcelUtil.read(filePath, 0, ImportTransfer.class);
            if (ObjectUtil.isEmpty(readList)) {
                throw new ServiceException("导入文件内容不能为空");
            }
            for (ImportTransfer importTransfer : readList) {
                ValidationUtils.validate(importTransfer);
            }
            // 获取车架号列表
            List<String> vinList = readList.stream().map(ImportTransfer::getVin).distinct().collect(Collectors.toList());
            if (vinList.size() != readList.size()) {
                throw new ServiceException("导入车架号不能重复");
            }
            List<VehicleTransferFixedResponse> list = transferFixedService.getVehicleTransferFixedResponse(vinList);
            if (CollectionUtil.isEmpty(list)) {
                throw new ServiceException("导入车架号不存在");
            }
            for (VehicleTransferFixedResponse response : list) {
                if (response.getAssetCompanyId() == null){
                    throw new ServiceException(StrUtil.format("{}-车辆资产所属公司不能为空", response.getVin()));
                }
                if (response.getOwnOrganizationId() == null){
                    throw new ServiceException(StrUtil.format("{}-车辆实际运营公司(所属)不能为空", response.getVin()));
                }
                if (response.getUsageOrganizationId() == null){
                    throw new ServiceException(StrUtil.format("{}-车辆实际运营公司(使用)不能为空", response.getVin()));
                }
            }
            CommonUtils.deleteIfExists(request.getFilePath());
            return ResultResponse.success(list);
        } catch (Exception exception) {
            CommonUtils.deleteIfExists(request.getFilePath());
            log.error("导入转固车辆数据信息异常", exception);
            if (exception instanceof ServiceException) {
                throw new ServiceException(StrUtil.format("{},请重新上传", exception.getMessage()));
            }
            throw new ServiceException("批量导入转固信息失败,请重新上传");
        }
    }


    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "刷新转固审批结果", httpMethod = "POST")
    @RequestMapping(value = "refreshTransferFixed", method = RequestMethod.POST)
    public ResultResponse refreshTransferFixed(@RequestBody RefreshDingTalkApprovalRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        return transferFixedService.refreshTransferFixed(request,tokenUserInfo);
    }
}
