package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.dazhong.transportation.vlms.config.DingTalkConfig;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.database.*;
import com.dazhong.transportation.vlms.dto.*;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.*;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.DocumentStatusEnum;
import com.dazhong.transportation.vlms.enums.FileBusinessTypeEnum;
import com.dazhong.transportation.vlms.enums.OperateLogBusinessTypeEnum;
import com.dazhong.transportation.vlms.enums.PropertyStatusEnum;
import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleDetail;
import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleDetailBusinessSell;
import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleDetailDisposal;
import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleInfo;
import com.dazhong.transportation.vlms.excel.util.ExcelUtil;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.*;
import com.dazhong.transportation.vlms.service.IDataDictService;
import com.dazhong.transportation.vlms.service.IDingTalkService;
import com.dazhong.transportation.vlms.service.IVehicleDisposalService;
import com.dazhong.transportation.vlms.service.IVehicleService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VehicleDisposalServiceImpl implements IVehicleDisposalService {

    @Autowired
    private TableVehicleDisposalService tableVehicleDisposalService;

    @Autowired
    private TableVehicleDisposalDetailService tableVehicleDisposalDetailService;

    @Autowired
    private TableVehicleDisposalDingTalkResultService tableVehicleDisposalDingTalkResultService;

    @Autowired
    private TableVehicleReverseDisposalService tableVehicleReverseDisposalService;

    @Autowired
    private TableApplyFileService tableApplyFileService;

    @Autowired
    private TableLicensePlateTaskInfoService tableLicensePlateTaskInfoService;

    @Autowired
    private TableVehicleModelInfoService tableVehicleModelInfoService;

    @Autowired
    private TableVehicleService tableVehicleService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Autowired
    private DingTalkConfig dingTalkConfig;

    @Autowired
    private IDingTalkService dingTalkService;

    @Autowired
    private IVehicleService vehicleService;

    @Autowired
    private IDataDictService dataDictService;

    @Override
    public PageResponse<VehicleDisposalListDto> queryVehicleDisposalPageResponse(SearchVehicleDisposalListRequest request, TokenUserInfo tokenUserInfo) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehicleDisposalListDto> vehicleDisposalListDtoList = tableVehicleDisposalService.queryVehicleDisposalList(request, tokenUserInfo);
        if (CollectionUtil.isNotEmpty(vehicleDisposalListDtoList)) {
            for (VehicleDisposalListDto vehicleDisposalListDto : vehicleDisposalListDtoList) {
                List<VehicleDisposalDetail> vehicleDisposalDetailList = tableVehicleDisposalDetailService.getVehicleDisposalDetailListByDisposalId(vehicleDisposalListDto.getId());
                vehicleDisposalListDto.setVehicleNumber(vehicleDisposalDetailList.size());
            }
        }
        PageInfo<VehicleDisposalListDto> pageInfo = new PageInfo<>(vehicleDisposalListDtoList);

        return new PageResponse<>(pageInfo.getTotal(), vehicleDisposalListDtoList);
    }

    @Override
    public ResultResponse<Void> relateReturnLicenseTask(Long id, String taskNumber, TokenUserInfo tokenUserInfo, boolean isSaveLog) {
        LicensePlateTaskInfo taskInfo = tableLicensePlateTaskInfoService.selectByTaskNumber(taskNumber);
        if (null == taskInfo || taskInfo.getTaskType() != 2) {
            throw new ServiceException("退牌任务不存在！");
        }
        VehicleDisposal vehicleDisposal = tableVehicleDisposalService.selectById(id);
        if (null == vehicleDisposal) {
            throw new ServiceException("处置申请单不存在！");
        }
        if (!Objects.equals(vehicleDisposal.getDocumentStatus(), DocumentStatusEnum.REVIEW_APPROVED.getCode())) {
            throw new ServiceException("处置申请单状态不是【审批通过】，不能关联退牌任务！");
        }

        // 重新关联退牌任务
        VehicleDisposal updateVehicleDisposal = new VehicleDisposal();
        updateVehicleDisposal.setId(id);
        updateVehicleDisposal.setTaskNumber(taskNumber);
        tableVehicleDisposalService.updateSelectiveById(updateVehicleDisposal, tokenUserInfo);

        if (isSaveLog) {
            // 操作日志
            tableOperateLogService.insertLog(vehicleDisposal.getId(), OperateLogBusinessTypeEnum.DISPOSAL_APPLICATION.getCode(), null, "关联退牌任务编号【" + taskNumber + "】", tokenUserInfo);
        }

        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse<Long> saveApplication(SaveDisposalApplicationRequest request, TokenUserInfo tokenUserInfo) {
        List<String> vinList = request.getVehicleDisposalDetailList().stream().map(VehicleDisposalDetailDto::getVin).collect(Collectors.toList());
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        for (VehicleInfo vehicleInfo : vehicleInfoList) {
            if (!vehicleInfo.getPropertyStatus().equals(PropertyStatusEnum.FIXED_ASSET.getCode())) {
                throw new ServiceException("车架号【" + vehicleInfo.getVin() + "】车辆状态不是固定资产，请检查！");
            }
            if (request.getProductLine() == 1 || request.getProductLine() == 4 || request.getProductLine() == 5) {
                GetCarInfoResponse.CarInfo carInfo =  vehicleService.getCarInfo(vehicleInfo.getVin());
                if (null == carInfo) {
                    throw new ServiceException("车架号【" + vehicleInfo.getVin() + "】在车辆核心系统不存在！");
                }
                if (carInfo.getCarStatusId() == 1 || carInfo.getCarStatusId() == 2) {
                    // 更新车辆状态为车辆核心系统状态（使用独立事务）
                    tableVehicleService.updateVehicleOperatingStatusInNewTransaction(vehicleInfo.getVin(),
                            carInfo.getCarStatusId());
                }
                // carStatusId 1 停用 2 在运
                if (carInfo.getCarStatusId() == 2) {
                    throw new ServiceException("车架号【" + vehicleInfo.getVin() + "】核心系统车辆为在运状态，不能发起申请，请联系巡网业务同事处理！");
                }
            }
        }

        // 1、保存车辆处置申请单
        VehicleDisposal vehicleDisposal = BeanUtil.copyProperties(request, VehicleDisposal.class);
        if (null == request.getId()) {
            String documentNo = StringUtils.EMPTY;
            if (request.getDocumentType() == 1) {
                documentNo = "CS" + new SimpleDateFormat("yyyyMMddHHmmssSSS", Locale.CHINESE).format(new Date());
            }
            if (request.getDocumentType() == 2) {
                documentNo = "BF" + new SimpleDateFormat("yyyyMMddHHmmssSSS", Locale.CHINESE).format(new Date());
            }
            if (request.getDocumentType() == 3) {
                documentNo = "SS" + new SimpleDateFormat("yyyyMMddHHmmssSSS", Locale.CHINESE).format(new Date());
            }
            vehicleDisposal.setDocumentNo(documentNo);
            tableVehicleDisposalService.insert(vehicleDisposal, tokenUserInfo);
        } else {
            vehicleDisposal.setCreateOperId(tokenUserInfo.getUserId());
            vehicleDisposal.setCreateOperName(tokenUserInfo.getName());
            tableVehicleDisposalService.updateSelectiveById(vehicleDisposal, tokenUserInfo);
        }

        // 2、保存车辆处置申请单明细
        if (CollectionUtil.isNotEmpty(request.getVehicleDisposalDetailList())) {
            List<String> distinctVinList = request.getVehicleDisposalDetailList().stream().map(VehicleDisposalDetailDto::getVin).distinct().collect(Collectors.toList());
            if (distinctVinList.size() != request.getVehicleDisposalDetailList().size()) {
                throw new ServiceException("明细数据异常，重复车架号！");
            }
        }
        
        tableVehicleDisposalDetailService.deleteByDisposalId(vehicleDisposal.getId());
        List<VehicleDisposalDetail> vehicleDisposalDetailList = BeanUtil.copyToList(request.getVehicleDisposalDetailList(), VehicleDisposalDetail.class);
        for (VehicleDisposalDetail vehicleDisposalDetail : vehicleDisposalDetailList) {
            vehicleDisposalDetail.setDisposalId(vehicleDisposal.getId());
        }
        tableVehicleDisposalDetailService.batchInsert(vehicleDisposalDetailList, tokenUserInfo);

        // 申请单提交逻辑
        if (request.getIsSubmit() == 1) {
            for (VehicleInfo vehicleInfo : vehicleInfoList) {
                // 更新资产状态为-处置审批中
                VehicleInfo updateVehicleInfo = new VehicleInfo();
                updateVehicleInfo.setId(vehicleInfo.getId());
                updateVehicleInfo.setPropertyStatus(PropertyStatusEnum.DISPOSAL_APPROVAL.getCode());
                tableVehicleService.updateVehicle(updateVehicleInfo, tokenUserInfo);

                // 操作日志
                tableOperateLogService.insertLog(vehicleInfo.getId(), OperateLogBusinessTypeEnum.VEHICLE_MASTER_DATA.getCode(), null, "提交处置申请单，资产状态改为【处置审批中】", tokenUserInfo);
            }
        }


        // 3、添加附件
        if (CollectionUtil.isNotEmpty(request.getVehicleApplyFileList())) {
            tableApplyFileService.deleteApplyFile(vehicleDisposal.getId(), FileBusinessTypeEnum.DISPOSAL_APPLICATION.getCode());
            if (CollectionUtil.isNotEmpty(request.getVehicleApplyFileList())) {
                for (VehicleApplyFileDto vehicleApplyFileDto : request.getVehicleApplyFileList()) {
                    VehicleApplyFile vehicleApplyFile = new VehicleApplyFile();
                    BeanUtil.copyProperties(vehicleApplyFileDto, vehicleApplyFile);
                    if (vehicleApplyFile.getFileUrl().contains(Global.instance.mfsUrl)) {
                        vehicleApplyFile.setFileUrl(vehicleApplyFile.getFileUrl().replace(Global.instance.mfsUrl + "/", ""));
                    }
                    vehicleApplyFile.setForeignId(vehicleDisposal.getId());
                    vehicleApplyFile.setBusinessType(FileBusinessTypeEnum.DISPOSAL_APPLICATION.getCode());
                    tableApplyFileService.insert(vehicleApplyFile, tokenUserInfo);
                }
            }
        }

        return ResultResponse.success(vehicleDisposal.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse<Long> submitApplication(SaveDisposalApplicationRequest request, TokenUserInfo tokenUserInfo) {
        // 保存处置申请单
        request.setIsSubmit(1);
        ResultResponse<Long> longResultResponse = saveApplication(request, tokenUserInfo);
        // 获取字典表
        List<String> systemCodeList = new ArrayList<>();
        systemCodeList.add("usage");
        systemCodeList.add("vehicleAbbreviation");
        systemCodeList.add("vehicleColor");
        Map<String, Map<Integer, String>> dictInfoMap = dataDictService.getDataMaintainDictMap(systemCodeList);
        // 提交钉钉审批流
        CreateDingTalkWorkFlowRequest createDingTalkWorkFlowRequest = new CreateDingTalkWorkFlowRequest();
        if (request.getDocumentType() == 1) {
            createDingTalkWorkFlowRequest.setProcessCode(dingTalkConfig.getVehicleDisposalProcessCode());
        }
        if (request.getDocumentType() == 2) {
            createDingTalkWorkFlowRequest.setProcessCode(dingTalkConfig.getVehicleScrapProcessCode());
        }
        if (request.getDocumentType() == 3) {
            createDingTalkWorkFlowRequest.setProcessCode(dingTalkConfig.getVehicleBusinessSellProcessCode());
        }
        createDingTalkWorkFlowRequest.setProcessComponentValues(request.convertToDingTalkData(dictInfoMap));
        createDingTalkWorkFlowRequest.setOriginatorUserId(tokenUserInfo.getDingTalkNum());
        createDingTalkWorkFlowRequest.setOriginatorDeptId(request.getOriginatorDeptId());
        if (CollectionUtil.isNotEmpty(request.getVehicleApplyFileList())) {
            CommitDingFlowAttachmentDto attachmentDto = new CommitDingFlowAttachmentDto();
            attachmentDto.setName("附件");
            attachmentDto.setAttachmentFileList(new ArrayList<>());
            String mfsUrl = Global.instance.mfsUrl;
            String mfsRootPath = Global.instance.mfsRootPath;
            for (VehicleApplyFileDto fileDto : request.getVehicleApplyFileList()) {
                if (fileDto.getFileUrl().contains(mfsUrl)) {
                    fileDto.setFileUrl(fileDto.getFileUrl().replace(Global.instance.mfsUrl + "/", ""));
                }
                if (!fileDto.getFileUrl().contains(mfsRootPath)) {
                    fileDto.setFileUrl(mfsRootPath + "/" + fileDto.getFileUrl());
                }
                DingFlowAttachmentFileInfoDto dingFlowAttachmentFileInfoDto = new DingFlowAttachmentFileInfoDto();
                dingFlowAttachmentFileInfoDto.setFileName(fileDto.getFileName());
                dingFlowAttachmentFileInfoDto.setFile(new File(fileDto.getFileUrl()));
                attachmentDto.getAttachmentFileList().add(dingFlowAttachmentFileInfoDto);
            }
            createDingTalkWorkFlowRequest.setAttachmentInfo(attachmentDto);
        }
        try {
            String dingTalkNo = dingTalkService.createDingTalkWorkFlow(createDingTalkWorkFlowRequest);
            // 更新钉钉审批单号
            VehicleDisposal updateVehicleDisposal = new VehicleDisposal();
            updateVehicleDisposal.setId(longResultResponse.getData());
            updateVehicleDisposal.setDingTalkNo(dingTalkNo);
            updateVehicleDisposal.setSubmitDate(new Date());
            updateVehicleDisposal.setDocumentStatus(DocumentStatusEnum.UNDER_REVIEW.getCode());
            tableVehicleDisposalService.updateSelectiveById(updateVehicleDisposal, tokenUserInfo);

            // 操作日志
            tableOperateLogService.insertLog(longResultResponse.getData(), OperateLogBusinessTypeEnum.DISPOSAL_APPLICATION.getCode(), null, "提交申请", tokenUserInfo);
        } catch (Exception e) {
            log.error("提交钉钉审批单异常", e);
            throw new ServiceException("提交至钉钉审批单异常！");
        }

        return longResultResponse;
    }

    @Override
    public ResultResponse<Void> cancelApplication(Long id, TokenUserInfo tokenUserInfo) {
        VehicleDisposal vehicleDisposal = tableVehicleDisposalService.selectById(id);
        if (null == vehicleDisposal) {
            throw new ServiceException("处置申请单不存在！");
        }
        if (!vehicleDisposal.getDocumentStatus().equals(DocumentStatusEnum.NOT_SUBMITTED.getCode()) &&
                !vehicleDisposal.getDocumentStatus().equals(DocumentStatusEnum.REVIEW_REJECTED.getCode())) {
            throw new ServiceException("处置申请单现状态无法作废！");
        }

        // 更新状态为已作废
        VehicleDisposal updateVehicleDisposal = new VehicleDisposal();
        updateVehicleDisposal.setId(id);
        updateVehicleDisposal.setDocumentStatus(DocumentStatusEnum.CANCELLED.getCode());
        tableVehicleDisposalService.updateSelectiveById(updateVehicleDisposal, tokenUserInfo);

        // 操作日志
        tableOperateLogService.insertLog(vehicleDisposal.getId(), OperateLogBusinessTypeEnum.DISPOSAL_APPLICATION.getCode(), null, "审批作废", tokenUserInfo);

        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse<Void> withdrawApplication(Long id, TokenUserInfo tokenUserInfo) {
        VehicleDisposal vehicleDisposal = tableVehicleDisposalService.selectById(id);
        if (null == vehicleDisposal) {
            throw new ServiceException("处置申请单不存在！");
        }
        if (!vehicleDisposal.getDocumentStatus().equals(DocumentStatusEnum.UNDER_REVIEW.getCode())) {
            throw new ServiceException("处置申请单现状态无法撤回！");
        }

        // 更新状态为已作废
        VehicleDisposal updateVehicleDisposal = new VehicleDisposal();
        updateVehicleDisposal.setId(id);
        updateVehicleDisposal.setDocumentStatus(DocumentStatusEnum.CANCELLED.getCode());
        tableVehicleDisposalService.updateSelectiveById(updateVehicleDisposal, tokenUserInfo);

        // 操作日志
        tableOperateLogService.insertLog(vehicleDisposal.getId(), OperateLogBusinessTypeEnum.DISPOSAL_APPLICATION.getCode(), null, "审批撤回", tokenUserInfo);

        List<VehicleDisposalDetail> vehicleDisposalDetailList = tableVehicleDisposalDetailService.getVehicleDisposalDetailListByDisposalId(vehicleDisposal.getId());
        for (VehicleDisposalDetail vehicleDisposalDetail : vehicleDisposalDetailList) {
            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vehicleDisposalDetail.getVin());
            if (null != vehicleInfo) {
                VehicleInfo updateVehicleInfo = new VehicleInfo();
                updateVehicleInfo.setId(vehicleInfo.getId());
                updateVehicleInfo.setPropertyStatus(PropertyStatusEnum.FIXED_ASSET.getCode());
                tableVehicleService.updateVehicle(updateVehicleInfo, tokenUserInfo);

                // 操作日志
                tableOperateLogService.insertLog(vehicleInfo.getId(), OperateLogBusinessTypeEnum.VEHICLE_MASTER_DATA.getCode(), null, "撤回处置申请单，资产状态改为【固定资产】", tokenUserInfo);
            }
        }

        // 调用钉钉撤回服务
        TerminateDingTalkProcessInstanceRequest processInstanceRequest = new TerminateDingTalkProcessInstanceRequest();
        processInstanceRequest.setSystem(true);
        processInstanceRequest.setProcessInstanceId(vehicleDisposal.getDingTalkNo());
        processInstanceRequest.setRemark("处置申请撤回");
        dingTalkService.terminateProcessInstance(processInstanceRequest);

        return ResultResponse.success();
    }

    @Override
    public DisposalApplicationDetailResponse queryDisposalDetail(Long id) {
        VehicleDisposal vehicleDisposal = tableVehicleDisposalService.selectById(id);
        DisposalApplicationDetailResponse result = BeanUtil.copyProperties(vehicleDisposal, DisposalApplicationDetailResponse.class);

        // 明细数据
        List<VehicleDisposalDetail> vehicleDisposalDetailList = tableVehicleDisposalDetailService.getVehicleDisposalDetailListByDisposalId(vehicleDisposal.getId());
        result.setVehicleDisposalDetailList(BeanUtil.copyToList(vehicleDisposalDetailList, VehicleDisposalDetailDto.class));
        result.setVehicleNumber(result.getVehicleDisposalDetailList().size());
        for (VehicleDisposalDetailDto vehicleDisposalDetailDto : result.getVehicleDisposalDetailList()) {
            VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(vehicleDisposalDetailDto.getVehicleModelId());
            if (null != vehicleModelInfo) {
                vehicleDisposalDetailDto.setVehicleModelName(vehicleModelInfo.getVehicleModelName());
            }
            if (StringUtils.isNotBlank(vehicleDisposalDetailDto.getSecondhandCarInvoiceUrl())) {
                String secondhandCarInvoiceUrl = vehicleDisposalDetailDto.getSecondhandCarInvoiceUrl().replace(Global.instance.mfsRootPath + "/", "");
                vehicleDisposalDetailDto.setSecondhandCarInvoiceUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, secondhandCarInvoiceUrl));
            }
        }

        // 附件
        List<VehicleApplyFileDto> applyFileDtoList = new ArrayList<>();
        List<VehicleApplyFile> vehicleApplyFileList = tableApplyFileService.queryFileList(id, FileBusinessTypeEnum.DISPOSAL_APPLICATION.getCode());
        vehicleApplyFileList.forEach(record -> {
            VehicleApplyFileDto fileDto = BeanUtil.copyProperties(record, VehicleApplyFileDto.class);
            String fileUrl = fileDto.getFileUrl().replace(Global.instance.mfsRootPath + "/", "");
            fileDto.setFileUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, fileUrl));
            applyFileDtoList.add(fileDto);
        });
        result.setVehicleApplyFileDtoList(applyFileDtoList);

        // 钉钉回调信息
        VehicleDisposalDingTalkResult vehicleDisposalDingTalkResult = tableVehicleDisposalDingTalkResultService.getVehicleDisposalDingTalkResultByDisposalId(vehicleDisposal.getId());
        result.setVehicleDisposalDingTalkResult(BeanUtil.copyProperties(vehicleDisposalDingTalkResult, VehicleDisposalDingTalkResultDto.class));

        return result;
    }

    @Override
    public List<DisposalDingTalkDetailDto> queryDisposalDingTalkDetailList(Long id) {
        VehicleDisposal vehicleDisposal = tableVehicleDisposalService.selectById(id);
        if (null == vehicleDisposal || !Objects.equals(vehicleDisposal.getDocumentStatus(), DocumentStatusEnum.REVIEW_APPROVED.getCode())) {
            return new ArrayList<>();
        }
        // 明细数据
        List<VehicleDisposalDetail> vehicleDisposalDetailList = tableVehicleDisposalDetailService.getVehicleDisposalDetailListByDisposalId(id);
        List<DisposalDingTalkDetailDto> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(vehicleDisposalDetailList)) {
            for (VehicleDisposalDetail record : vehicleDisposalDetailList) {
                DisposalDingTalkDetailDto resultRecord = BeanUtil.copyProperties(record, DisposalDingTalkDetailDto.class);
                if (StringUtils.isNotBlank(record.getFileUrl())) {
                    List<String> fileUrlList = new ArrayList<>();
                    for (String fileUrl : record.getFileUrl().split(",")) {
                        fileUrlList.add(StrUtil.format("{}/{}", Global.instance.mfsUrl, fileUrl.replace(Global.instance.mfsRootPath + "/", "")));
                    }
                    resultRecord.setFileUrlList(fileUrlList);
                }
                if (StringUtils.isNotBlank(record.getActualSaleFileUrl())) {
                    List<String> actualSaleFileUrlList = new ArrayList<>();
                    for (String actualSaleFileUrl : record.getActualSaleFileUrl().split(",")) {
                        actualSaleFileUrlList.add(StrUtil.format("{}/{}", Global.instance.mfsUrl, actualSaleFileUrl.replace(Global.instance.mfsRootPath + "/", "")));
                    }
                    resultRecord.setActualSaleFileUrlList(actualSaleFileUrlList);
                }
                result.add(resultRecord);
            }
        }

        return result;
    }

    @Override
    public void dingTalkResultProcess(String dingTalkNo, String dingTalkResult) {
        if (dingTalkResult.equals("agree")) {
            dingTalkResultAgree(dingTalkNo);
        } else if (dingTalkResult.equals("refuse")) {
            dingTalkResultRefuse(dingTalkNo);
        } else if (dingTalkResult.equals("terminate")) {
            dingTalkResultTerminate(dingTalkNo);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void dingTalkResultAgree(String dingTalkNo) {
        VehicleDisposal vehicleDisposal = tableVehicleDisposalService.selectByDingTalkNo(dingTalkNo);
        if (null == vehicleDisposal) {
            throw new ServiceException("车辆处置申请单不存在！");
        }
        TokenUserInfo dingTalkUserInfo = new TokenUserInfo();
        dingTalkUserInfo.setUserId(-1L);
        dingTalkUserInfo.setName("钉钉审批回调");

        // 变更申请单状态-审批通过
        VehicleDisposal updateVehicleDisposal = new VehicleDisposal();
        updateVehicleDisposal.setId(vehicleDisposal.getId());
        updateVehicleDisposal.setDocumentStatus(DocumentStatusEnum.REVIEW_APPROVED.getCode());
        tableVehicleDisposalService.updateSelectiveById(updateVehicleDisposal);

        // 操作日志
        tableOperateLogService.insertLog(vehicleDisposal.getId(), OperateLogBusinessTypeEnum.DISPOSAL_APPLICATION.getCode(), null, "审批通过", dingTalkUserInfo);

        GetDingTalkWorkFlowRequest queryFormInstanceRequest = new GetDingTalkWorkFlowRequest();
        queryFormInstanceRequest.setInstanceId(vehicleDisposal.getDingTalkNo());
        GetDingTalkDetailResponse getDingTalkDetailResponse = dingTalkService.getDingTalkDetailFlow(queryFormInstanceRequest);
        
        List<VehicleDisposalDetailDto> vehicleDisposalDetailDtoList = new ArrayList<>();
        // 处置申请单
        if (vehicleDisposal.getDocumentType() == 1) {
            List<RowDto> rowDtoList = getDingTalkDetailResponse.getDetailData();
            for (RowDto rowDto : rowDtoList) {
                VehicleDisposalDetailDto vehicleDisposalDetailDto = rowDto.convertToVehicleDisposalDetailDto();
                vehicleDisposalDetailDtoList.add(vehicleDisposalDetailDto);
            }
        }
        // 报废申请单
        if (vehicleDisposal.getDocumentType() == 2) {
            vehicleDisposalDetailDtoList.add(getDingTalkDetailResponse.convertToVehicleDisposalDetailDto());
        }
        // 商售申请单
        if (vehicleDisposal.getDocumentType() == 3) {
            VehicleDisposalDingTalkResult vehicleDisposalDingTalkResult = getDingTalkDetailResponse.convertToVehicleDisposalDingTalkResultDto();
            vehicleDisposalDingTalkResult.setDisposalId(vehicleDisposal.getId());
            tableVehicleDisposalDingTalkResultService.insert(vehicleDisposalDingTalkResult, dingTalkUserInfo);
        }

        // 遍历处置明细
        for (VehicleDisposalDetailDto vehicleDisposalDetailDto : vehicleDisposalDetailDtoList) {
            VehicleDisposalDetail updateVehicleDisposalDetail = BeanUtil.copyProperties(vehicleDisposalDetailDto, VehicleDisposalDetail.class);
            updateVehicleDisposalDetail.setDisposalId(vehicleDisposal.getId());
            // 获取附件信息
            if (CollectionUtils.isNotEmpty(vehicleDisposalDetailDto.getDingTalkFileList())
                    || CollectionUtils.isNotEmpty(vehicleDisposalDetailDto.getActualSaleDingTalkFileList())) {
                updateVehicleDisposalDetail.setFileUrl(getDingTalkFileUrl(dingTalkNo, vehicleDisposalDetailDto.getDingTalkFileList()));
                updateVehicleDisposalDetail.setActualSaleFileUrl(getDingTalkFileUrl(dingTalkNo, vehicleDisposalDetailDto.getActualSaleDingTalkFileList()));
                tableVehicleDisposalDetailService.updateDingTalkInfo(updateVehicleDisposalDetail);
            }

            // 存在逆处置流程
            VehicleReverseDisposalDetailListDto reverseDisposalDetailListDto = tableVehicleReverseDisposalService.queryVehicleReverseDisposalDetail(vehicleDisposalDetailDto.getVin(), vehicleDisposal.getDocumentNo());
            if (null != reverseDisposalDetailListDto) {
                continue;
            }

            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vehicleDisposalDetailDto.getVin());
            VehicleInfo updateVehicleInfo = new VehicleInfo();
            updateVehicleInfo.setVin(vehicleDisposalDetailDto.getVin());
            if (vehicleDisposal.getDocumentType() == 1) {
                if (vehicleDisposalDetailDto.getRealRetirementDate() != null){
                    updateVehicleInfo.setRealRetirementDate(vehicleDisposalDetailDto.getRealRetirementDate());
                }
                // 已处置
                updateVehicleInfo.setPropertyStatus(PropertyStatusEnum.DISPOSED.getCode());
                // 操作日志
                tableOperateLogService.insertLog(vehicleInfo.getId(), OperateLogBusinessTypeEnum.VEHICLE_MASTER_DATA.getCode(), null, "处置申请单通过，资产状态改为【已处置】", dingTalkUserInfo);
            } else if (vehicleDisposal.getDocumentType() == 2) {
                // 已报废
                updateVehicleInfo.setPropertyStatus(PropertyStatusEnum.SCRAPPED.getCode());
                updateVehicleInfo.setRealRetirementDate(new Date());
                // 操作日志
                tableOperateLogService.insertLog(vehicleInfo.getId(), OperateLogBusinessTypeEnum.VEHICLE_MASTER_DATA.getCode(), null, "处置申请单通过，资产状态改为【已报废】", dingTalkUserInfo);
            }
            tableVehicleService.updateVehicleByVinSelective(updateVehicleInfo);
        }
    }

    /**
     * 获取钉钉附件地址
     *
     * @param dingTalkNo
     * @param dingTalkFileList
     * @return
     */
    private String getDingTalkFileUrl(String dingTalkNo, List<DingTalkFileDto> dingTalkFileList) {
        // 获取附件信息
        if (CollectionUtil.isNotEmpty(dingTalkFileList)) {
            try {
                List<String> fileUrlList = new ArrayList<>();
                for (DingTalkFileDto dingTalkFileDto : dingTalkFileList) {
                    String fileUrl = dingTalkService.getInstanceDownloadFile(dingTalkNo, dingTalkFileDto.getFileId(), dingTalkFileDto.getFileName());
                    fileUrlList.add(fileUrl);
                }
                if (CollectionUtil.isNotEmpty(fileUrlList)) {
                    return String.join(",", fileUrlList);
                }
            } catch (ServiceException e) {
                log.error("钉钉审批单【{}】，更新附件地址异常！", dingTalkNo, e);
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void dingTalkResultRefuse(String dingTalkNo) {
        VehicleDisposal vehicleDisposal = tableVehicleDisposalService.selectByDingTalkNo(dingTalkNo);
        if (null == vehicleDisposal) {
            throw new ServiceException("车辆主数据申请单不存在！");
        }
        TokenUserInfo dingTalkUserInfo = new TokenUserInfo();
        dingTalkUserInfo.setUserId(-1L);
        dingTalkUserInfo.setName("钉钉审批回调");
        // 变更申请单状态-审批拒绝
        VehicleDisposal updateVehicleDisposal = new VehicleDisposal();
        updateVehicleDisposal.setId(vehicleDisposal.getId());
        updateVehicleDisposal.setDocumentStatus(DocumentStatusEnum.REVIEW_REJECTED.getCode());
        tableVehicleDisposalService.updateSelectiveById(updateVehicleDisposal);

        // 操作日志
        tableOperateLogService.insertLog(vehicleDisposal.getId(), OperateLogBusinessTypeEnum.DISPOSAL_APPLICATION.getCode(), null, "审批拒绝", dingTalkUserInfo);

        GetDingTalkWorkFlowRequest queryFormInstanceRequest = new GetDingTalkWorkFlowRequest();
        queryFormInstanceRequest.setInstanceId(vehicleDisposal.getDingTalkNo());
        GetDingTalkDetailResponse getDingTalkDetailResponse = dingTalkService.getDingTalkDetailFlow(queryFormInstanceRequest);
        List<RowDto> rowDtoList = getDingTalkDetailResponse.getDetailData();
        for (RowDto rowDto : rowDtoList) {
            VehicleDisposalDetailDto vehicleDisposalDetailDto = rowDto.convertToVehicleDisposalDetailDto();
            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vehicleDisposalDetailDto.getVin());

            // 商售申请单-车辆修改状态特殊处理
            if (vehicleDisposal.getDocumentType() == 3) {
                if (!vehicleInfo.getPropertyStatus().equals(PropertyStatusEnum.DISPOSAL_APPROVAL.getCode()) &&
                        !vehicleInfo.getPropertyStatus().equals(PropertyStatusEnum.PENDING_DISPOSAL.getCode()) &&
                        !vehicleInfo.getPropertyStatus().equals(PropertyStatusEnum.PENDING_SCRAP.getCode()))
                    continue;
            }

            VehicleInfo updateVehicleInfo = new VehicleInfo();
            updateVehicleInfo.setVin(vehicleDisposalDetailDto.getVin());
            updateVehicleInfo.setPropertyStatus(PropertyStatusEnum.FIXED_ASSET.getCode());
            tableVehicleService.updateVehicleByVinSelective(updateVehicleInfo);

            // 操作日志
            tableOperateLogService.insertLog(vehicleInfo.getId(),
                    OperateLogBusinessTypeEnum.VEHICLE_MASTER_DATA.getCode(), null, "处置申请单拒绝，资产状态改为【固定资产】",
                    dingTalkUserInfo);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void dingTalkResultTerminate(String dingTalkNo) {
        VehicleDisposal vehicleDisposal = tableVehicleDisposalService.selectByDingTalkNo(dingTalkNo);
        if (null == vehicleDisposal) {
            throw new ServiceException("车辆处置申请单不存在！");
        }
        
        TokenUserInfo dingTalkUserInfo = new TokenUserInfo();
        dingTalkUserInfo.setUserId(-1L);
        dingTalkUserInfo.setName("钉钉审批回调");
        
        // 变更申请单状态-已作废
        VehicleDisposal updateVehicleDisposal = new VehicleDisposal();
        updateVehicleDisposal.setId(vehicleDisposal.getId());
        updateVehicleDisposal.setDocumentStatus(DocumentStatusEnum.CANCELLED.getCode());
        tableVehicleDisposalService.updateSelectiveById(updateVehicleDisposal, dingTalkUserInfo);
        
        // 操作日志
        tableOperateLogService.insertLog(vehicleDisposal.getId(), OperateLogBusinessTypeEnum.DISPOSAL_APPLICATION.getCode(), null, "钉钉审批流终止，审批撤回,申请单状态改为【已作废】", dingTalkUserInfo);
        
        // 获取处置明细
        List<VehicleDisposalDetail> detailList = tableVehicleDisposalDetailService.getVehicleDisposalDetailListByDisposalId(vehicleDisposal.getId());
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        
        // 将车辆状态恢复为固定资产
        for (VehicleDisposalDetail vehicleDisposalDetail : detailList) {
            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vehicleDisposalDetail.getVin());
            if (vehicleInfo != null) {
                VehicleInfo updateVehicleInfo = new VehicleInfo();
                updateVehicleInfo.setId(vehicleInfo.getId());
                updateVehicleInfo.setPropertyStatus(PropertyStatusEnum.FIXED_ASSET.getCode());
                tableVehicleService.updateVehicle(updateVehicleInfo, dingTalkUserInfo);
                
                // 操作日志
                tableOperateLogService.insertLog(vehicleInfo.getId(), OperateLogBusinessTypeEnum.VEHICLE_MASTER_DATA.getCode(), null, "钉钉审批流终止，资产状态恢复为【固定资产】", dingTalkUserInfo);
            }
        }
    }

    @Override
    public List<ImportDisposalVehicleDetail> getDisposalVehicleDetailList(ImportDisposalVehicleInfoRequest request) {
        if (null == request.getDocumentType()) {
            throw new ServiceException("单据类型不能为空");
        }

        List<ImportDisposalVehicleDetail> readList;
        if(request.getDocumentType() == 1) {
            readList = ExcelUtil.read(request.getFilePath(), 0, ImportDisposalVehicleDetailDisposal.class);
        } else {
            readList = ExcelUtil.read(request.getFilePath(), 0, ImportDisposalVehicleDetailBusinessSell.class);
        }
        
        if (ObjectUtil.isEmpty(readList)) {
            throw new ServiceException("上传文件不能为空");
        }
        if (readList.size() > 1000) {
            throw new ServiceException("最大行数1000行");
        }
        for (ImportDisposalVehicleDetail importDisposalVehicleDetail : readList) {
            ValidationUtils.validate(importDisposalVehicleDetail);
            if (StringUtils.isBlank(importDisposalVehicleDetail.getVin()) && StringUtils.isBlank(importDisposalVehicleDetail.getLicensePlate())) {
                continue;
            }
            ResultResponse<VehicleBasicResponse> response;
            if (StringUtils.isNotBlank(importDisposalVehicleDetail.getVin())) {
                response = vehicleService.getVehicleBasicInfo(importDisposalVehicleDetail.getVin());
            } else {
                response = vehicleService.getVehicleBasicInfoByLicensePlate(importDisposalVehicleDetail.getLicensePlate());
            }
            if (response.getCode() == 0) {
                BeanUtil.copyProperties(response.getData(), importDisposalVehicleDetail);
                importDisposalVehicleDetail.setUsedMonths(response.getData().getMonthsUsed());
                // 投产日期
                Date startDate = response.getData().getStartDate();
                if (startDate != null) {
                    importDisposalVehicleDetail.setStartDate(DateUtil.format(startDate, "yyyy-MM-dd"));
                }
                // 上牌日期
                Date licenseDate = response.getData().getLicenseDate();
                if (licenseDate != null) {
                    importDisposalVehicleDetail.setLicenseDate(DateUtil.format(licenseDate, "yyyy-MM-dd"));
                }
                if(null != response.getData().getPriceOnAutohome()) {
                    importDisposalVehicleDetail.setMsrp(response.getData().getPriceOnAutohome().toString());
                }
            }
        }

        return readList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse<Void> saveSecondhandCarInvoiceUrls(List<UploadFileResponse> urlList, Long disposalId, TokenUserInfo tokenUserInfo) {
        // 验证处置申请单是否存在
        VehicleDisposal vehicleDisposal = tableVehicleDisposalService.selectById(disposalId);
        if (null == vehicleDisposal) {
            throw new ServiceException("处置申请单不存在！");
        }
        if (vehicleDisposal.getDocumentStatus() == 3) {
            throw new ServiceException("单据状态为【审批拒绝】，商售申请单不能保存二手车交易发票");
        }

        // 获取该处置申请单的所有明细
        List<VehicleDisposalDetail> detailList = tableVehicleDisposalDetailService.getVehicleDisposalDetailListByDisposalId(vehicleDisposal.getId());
        if (CollectionUtil.isEmpty(detailList)) {
            throw new ServiceException("处置申请单明细不存在！");
        }

        // 按车架号分组明细记录
        Map<String, VehicleDisposalDetail> vinDetailMap = detailList.stream()
                .collect(Collectors.toMap(VehicleDisposalDetail::getVin, detail -> detail));

        // 按车架号分组URL
        Map<String, List<String>> vinUrlMap = new HashMap<>();
        for (UploadFileResponse uploadFileResponse : urlList) {
            // 从URL中提取文件名（车架号）
            String vin = uploadFileResponse.getFileName().substring(0, uploadFileResponse.getFileName().lastIndexOf("."));
            vinUrlMap.computeIfAbsent(vin, k -> new ArrayList<>()).add(uploadFileResponse.getFilePath().replace(Global.instance.mfsRootPath + "/", ""));
        }

        // 更新对应的VehicleDisposalDetail记录
        for (Map.Entry<String, List<String>> entry : vinUrlMap.entrySet()) {
            String vin = entry.getKey();
            List<String> urls = entry.getValue();

            VehicleDisposalDetail detail = vinDetailMap.get(vin);
            if (detail == null) {
                throw new ServiceException("车架号【" + vin + "】无法匹配到处置申请单明细记录");
            }

            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vin);
            if (!vehicleInfo.getPropertyStatus().equals(PropertyStatusEnum.DISPOSAL_APPROVAL.getCode()) &&
                    !vehicleInfo.getPropertyStatus().equals(PropertyStatusEnum.PENDING_DISPOSAL.getCode()) &&
                    !vehicleInfo.getPropertyStatus().equals(PropertyStatusEnum.PENDING_SCRAP.getCode())) {
                throw new ServiceException("车架号【" + vin + "】资产状态不是【处置审批中】、【待处置（未交付）】、【待报废（未交付）】，不能保存二手车交易发票");
            }

            VehicleInfo updateVehicleInfo = new VehicleInfo();
            updateVehicleInfo.setVin(vin);
            updateVehicleInfo.setPropertyStatus(PropertyStatusEnum.DISPOSED.getCode());
            tableVehicleService.updateVehicleByVinSelective(updateVehicleInfo);
            // 操作日志
            tableOperateLogService.insertLog(vehicleInfo.getId(), OperateLogBusinessTypeEnum.VEHICLE_MASTER_DATA.getCode(), null, "上传二手车交易发票，资产状态改为【已处置】", tokenUserInfo);
            
            VehicleDisposalDetail updateDetail = new VehicleDisposalDetail();
            updateDetail.setId(detail.getId());
            updateDetail.setSecondhandCarInvoiceUrl(urls.get(0));
            tableVehicleDisposalDetailService.updateSelectiveById(updateDetail, tokenUserInfo);

            // 操作日志
            tableOperateLogService.insertLog(vehicleDisposal.getId(), OperateLogBusinessTypeEnum.DISPOSAL_APPLICATION.getCode(), 
                    null, "保存车架号【" + vin + "】二手车交易发票", tokenUserInfo);
        }

        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse<Void> importDisposalVehicleInfo(ImportDisposalVehicleInfoRequest request, TokenUserInfo tokenUserInfo) {
        // 读取Excel文件
        List<ImportDisposalVehicleInfo> readList = ExcelUtil.read(request.getFilePath(), 0, ImportDisposalVehicleInfo.class);
        if (ObjectUtil.isEmpty(readList)) {
            throw new ServiceException("导入文件内容不能为空");
        }
        if (readList.size() > 1000) {
            throw new ServiceException("最大行数1000行");
        }

        // 校验Excel数据
        for (ImportDisposalVehicleInfo importInfo : readList) {
            ValidationUtils.validate(importInfo);
        }

        // 验证处置申请单是否存在
        VehicleDisposal vehicleDisposal = tableVehicleDisposalService.selectByDocumentNo(request.getDocumentNo());
        if (null == vehicleDisposal) {
            throw new ServiceException("处置申请单不存在！");
        }
        if (vehicleDisposal.getDocumentType() != 3) {
            throw new ServiceException("非商售申请单，不能导入处置信息！");
        }

        // 获取该处置申请单的所有明细
        List<VehicleDisposalDetail> detailList = tableVehicleDisposalDetailService.getVehicleDisposalDetailListByDisposalId(vehicleDisposal.getId());
        if (CollectionUtil.isEmpty(detailList)) {
            throw new ServiceException("处置申请单明细不存在！");
        }

        // 按车架号分组明细记录
        Map<String, VehicleDisposalDetail> vinDetailMap = detailList.stream()
                .collect(Collectors.toMap(VehicleDisposalDetail::getVin, detail -> detail));

        // 遍历Excel数据，更新明细记录
        for (ImportDisposalVehicleInfo importInfo : readList) {
            String vin = importInfo.getVin();
            VehicleDisposalDetail detail = vinDetailMap.get(vin);
            
            if (detail == null) {
                throw new ServiceException("车架号【" + vin + "】在处置申请单明细中不存在！");
            }

            // 更新明细记录
            VehicleDisposalDetail updateDetail = new VehicleDisposalDetail();
            updateDetail.setId(detail.getId());
            updateDetail.setActualSellingPrice(importInfo.getActualSellingPrice());
            updateDetail.setActualNetPrice(importInfo.getActualNetPrice());
            updateDetail.setNetPriceDiff(importInfo.getNetPriceDiff());
            updateDetail.setSaleGainLoss(importInfo.getSaleGainLoss());
            updateDetail.setRealRetirementDate(importInfo.getRealRetirementDate() != null
                    ? DateUtil.parse(importInfo.getRealRetirementDate(), "yyyy-MM-dd")
                    : null);
            
            tableVehicleDisposalDetailService.updateSelectiveById(updateDetail, tokenUserInfo);

            // 操作日志
            tableOperateLogService.insertLog(vehicleDisposal.getId(), OperateLogBusinessTypeEnum.DISPOSAL_APPLICATION.getCode(), 
                    null, "导入车架号【" + vin + "】处置信息", tokenUserInfo);
        }

        return ResultResponse.success();
    }
}
