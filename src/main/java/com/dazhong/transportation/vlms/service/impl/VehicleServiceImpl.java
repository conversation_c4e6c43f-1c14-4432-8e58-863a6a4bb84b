package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.*;
import com.dazhong.transportation.vlms.dto.*;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.response.*;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.*;
import com.dazhong.transportation.vlms.excel.*;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.*;
import com.dazhong.transportation.vlms.service.*;
import com.dazhong.transportation.vlms.utils.CommonUtils;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;


@Slf4j
@Service
public class VehicleServiceImpl implements IVehicleService {

    @Autowired
    private IFileService fileService;

    @Autowired
    private IDataDictService dataDictService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private TableVehicleService tableVehicleService;

    @Autowired
    private TableVehicleManagementLegacyService tableVehicleManagementLegacyService;

    @Autowired
    private TableVehicleDecorationService tableVehicleDecorationService;

    @Autowired
    private TableDataOwnerInfoService tableDataOwnerInfoService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Autowired
    private TableVehicleModelInfoService tableVehicleModelInfoService;

    @Autowired
    private TableVehicleOrderReceiptService tableVehicleOrderReceiptService;

    @Autowired
    private TableVehicleDisposalService tableVehicleDisposalService;

    @Autowired
    private TableVehicleFileRecordService tableVehicleFileRecordService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Autowired
    private ILicensePlateTaskInfoService licensePlateTaskInfoService;

    @Autowired
    private TableVehicleDepreciationDataService tableVehicleDepreciationDataService;

    @Autowired
    private TableTransferFixedDetailsService tableTransferFixedDetailsService;

    @Autowired
    private TableVehicleContractService tableVehicleContractService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public PageResponse searchInboundVehicleList(SearchInboundVehicleRequest request, TokenUserInfo tokenUserInfo) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<InboundVehicleResponse> list = tableVehicleService.searchInboundVehicleList(request, tokenUserInfo);
        PageInfo<InboundVehicleResponse> pageInfo = new PageInfo<>(list);
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(list);
        if (CollectionUtil.isNotEmpty(list)) {
            // 查询转固进行中的记录
            List<String> vinList = list.stream().map(InboundVehicleResponse::getVin).collect(Collectors.toList());
            List<VehicleTransferFixedResponse> vehicleTransferFixedResponseList = tableTransferFixedDetailsService.queryTransferFixedDetailsByVin(vinList);
            Map<String, VehicleTransferFixedResponse> vehicleTransferFixedResponseMap = vehicleTransferFixedResponseList.stream().collect(Collectors.toMap(VehicleTransferFixedResponse::getVin, vehicleTransferFixedResponse -> vehicleTransferFixedResponse));
            list.forEach(res -> {
                VehicleTransferFixedResponse fixedResponse = vehicleTransferFixedResponseMap.get(res.getVin());
                if (fixedResponse != null) {
                    if (fixedResponse != null) {
                        res.setApplyNo(fixedResponse.getApplyNo());
                        res.setCreateOperName(fixedResponse.getCreateOperName());
                        res.setCreateTime(fixedResponse.getCreateTime());
                        res.setApplyStatus(fixedResponse.getApplyStatus());
                    }
                }
            });
        }
        return pageResponse;
    }

    @Override
    public ResultResponse getInboundVehicleDetails(String vin) {
        InboundVehicleDetailsResponse response = new InboundVehicleDetailsResponse();
        VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vin);
        if (vehicleInfo == null) {
            return ResultResponse.businessFailed("车辆信息不存在");
        }
        response.setVin(vehicleInfo.getVin());
        response.setId(vehicleInfo.getId());
        VehicleManagementLegacyInfo vehicleManagementLegacyInfo = tableVehicleManagementLegacyService.queryVehicleByVin(vin);
        if (vehicleManagementLegacyInfo == null) {
            return ResultResponse.businessFailed("车辆信息不存在");
        }

        // 入库车辆收货信息
        VehiclePurchaseReceiptDto receiptInfo = tableVehicleOrderReceiptService.queryVehiclePurchaseReceipt(vin);
        if (receiptInfo != null) {
            response.setReceiptInfo(receiptInfo);
        }
        // 上牌信息
        List<LicensePlateTaskVehicleDetailDto> licensePlateList = licensePlateTaskInfoService.queryLicensePlateTaskList(vin, 1);
        if (CollectionUtil.isNotEmpty(licensePlateList)) {
            response.setLicensePlateList(licensePlateList);
        }

        // 装潢记录列表
        List<String> vinList = Arrays.asList(vin);
        List<VehicleDecoration> list = tableVehicleDecorationService.queryDecorationList(vinList);
        List<VehicleDecorationResponse> decorationResponseList = BeanUtil.copyToList(list, VehicleDecorationResponse.class);
        response.setDecorationList(decorationResponseList);

        // 车辆附件信息
        List<VehicleFileRecordResponse> fileResponseList = new ArrayList<>();
        String mfsUrl = Global.instance.mfsUrl;
        String vehicleLicenseUrl = vehicleInfo.getVehicleLicenseUrl();
        if (StringUtils.isNotBlank(vehicleLicenseUrl)) {
            VehicleFileRecordResponse vehicleFileRecordResponse = new VehicleFileRecordResponse();
            vehicleFileRecordResponse.setFileUrl(StrUtil.format("{}/{}", mfsUrl, vehicleLicenseUrl));
            vehicleFileRecordResponse.setFileType(VehicleAttachmentTypeEnum.vehicleLicense.getCode());
            fileResponseList.add(vehicleFileRecordResponse);
        }
        String certificateOwnershipUrl = vehicleInfo.getCertificateOwnershipUrl();
        if (StringUtils.isNotBlank(certificateOwnershipUrl)) {
            VehicleFileRecordResponse vehicleFileRecordResponse = new VehicleFileRecordResponse();
            vehicleFileRecordResponse.setFileUrl(StrUtil.format("{}/{}", mfsUrl, certificateOwnershipUrl));
            vehicleFileRecordResponse.setFileType(VehicleAttachmentTypeEnum.certificateOwnership.getCode());
            fileResponseList.add(vehicleFileRecordResponse);
        }
        String certificateConformityUrl = vehicleInfo.getCertificateConformityUrl();
        if (StringUtils.isNotBlank(certificateConformityUrl)) {
            VehicleFileRecordResponse vehicleFileRecordResponse = new VehicleFileRecordResponse();
            vehicleFileRecordResponse.setFileUrl(StrUtil.format("{}/{}", mfsUrl, certificateConformityUrl));
            vehicleFileRecordResponse.setFileType(VehicleAttachmentTypeEnum.certificateConformity.getCode());
            fileResponseList.add(vehicleFileRecordResponse);
        }
        String vehicleInvoiceUrl = vehicleInfo.getVehicleInvoiceUrl();
        if (StringUtils.isNotBlank(vehicleInvoiceUrl)) {
            VehicleFileRecordResponse vehicleFileRecordResponse = new VehicleFileRecordResponse();
            vehicleFileRecordResponse.setFileUrl(StrUtil.format("{}/{}", mfsUrl, vehicleInvoiceUrl));
            vehicleFileRecordResponse.setFileType(VehicleAttachmentTypeEnum.vehicleInvoice.getCode());
            fileResponseList.add(vehicleFileRecordResponse);
        }
        String purchaseTaxUrl = vehicleInfo.getPurchaseTaxUrl();
        if (StringUtils.isNotBlank(purchaseTaxUrl)) {
            VehicleFileRecordResponse vehicleFileRecordResponse = new VehicleFileRecordResponse();
            vehicleFileRecordResponse.setFileUrl(StrUtil.format("{}/{}", mfsUrl, purchaseTaxUrl));
            vehicleFileRecordResponse.setFileType(VehicleAttachmentTypeEnum.purchaseTax.getCode());
            fileResponseList.add(vehicleFileRecordResponse);
        }
        String operatingPermitUrl = vehicleInfo.getOperatingPermitUrl();
        if (StringUtils.isNotBlank(operatingPermitUrl)) {
            VehicleFileRecordResponse vehicleFileRecordResponse = new VehicleFileRecordResponse();
            vehicleFileRecordResponse.setFileUrl(StrUtil.format("{}/{}", mfsUrl, operatingPermitUrl));
            vehicleFileRecordResponse.setFileType(VehicleAttachmentTypeEnum.operatingPermit.getCode());
            fileResponseList.add(vehicleFileRecordResponse);
        }
        response.setFileRecordList(fileResponseList);

        // 车辆其他信息
        VehicleOtherInfoResponse otherInfo = BeanUtil.copyProperties(vehicleInfo, VehicleOtherInfoResponse.class);
        otherInfo.setUsageYears(vehicleInfo.getUsageAgeLimit());
        otherInfo.setDepreciationYears(vehicleInfo.getDepreciationAgeLimit());
        otherInfo.setOperationCategoryId(vehicleManagementLegacyInfo.getOperationCategoryId());
        otherInfo.setOperateTypeId(vehicleManagementLegacyInfo.getOperateTypeId());
        otherInfo.setVehicleCategoryId(vehicleManagementLegacyInfo.getVehicleCategoryId());
        otherInfo.setOperatingNo(vehicleManagementLegacyInfo.getOperatingNo());
        response.setOtherInfo(otherInfo);

        return ResultResponse.success(response);
    }

    @Override
    public PageResponse searchAssetVehicleList(SearchAssetVehicleRequest request, TokenUserInfo tokenUserInfo) {
        // 查询车辆列表
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehicleListResponse> list = tableVehicleService.searchAssetVehicleList(request, tokenUserInfo);
        PageInfo<VehicleListResponse> pageInfo = new PageInfo<>(list);
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(list);
        if (CollectionUtil.isNotEmpty(list)) {
            Date now = new Date();

            //查询字典表相关信息
            List<String> systemCodeList = Arrays.asList(
                    //商品车型
                    DataDictEnum.VEHICLE_ABBREVIATION.getValue(),
                    //车身颜色
                    DataDictEnum.VEHICLE_COLOR.getValue()
            );
            Map<String, Map<Integer, String>> dataMaintainDictMap = dataDictService.getDataMaintainDictMap(systemCodeList);

            // 查询组织信息
            List<OrgInfo> orgInfoList = tableOrgInfoService.queryAllOrgInfo();
            Map<Long, OrgInfo> orgMap = orgInfoList.stream().collect(Collectors.toMap(OrgInfo::getId, orgInfo -> orgInfo));
            list.forEach(res -> {
                OrgInfo orgInfo = orgMap.get(res.getOwnOrganizationId());
                if (orgInfo != null) {
                    res.setOwnOrganizationName(orgInfo.getCompanyName());
                }
                orgInfo = orgMap.get(res.getUsageOrganizationId());
                if (orgInfo != null) {
                    res.setUsageOrganizationName(orgInfo.getCompanyName());
                }
                // 计算两个日期间隔月份
                if (res.getRegistrationDateRegistrationCard() != null) {
                    long days = DateUtil.betweenDay(res.getRegistrationDateRegistrationCard(), now, true);
                    long months = days / 30;
                    long day = days % 30;
                    if (day > 0) {
                        months++;
                    }
                    res.setVehicleLife(String.valueOf(months));
                }
                //商品车型名称
                res.setVehicleColor(getDictValueName(DataDictEnum.VEHICLE_COLOR, res.getVehicleColorId(), dataMaintainDictMap));
                //商品车型名称
                res.setVehicleAbbreviation(getDictValueName(DataDictEnum.VEHICLE_ABBREVIATION, res.getVehicleAbbreviationId(), dataMaintainDictMap));
            });
        }
        return pageResponse;
    }

    @Override
    public ResultResponse<VehicleDetailsResponse> getVehicleDetails(String vin) {
        VehicleDetailsResponse response = new VehicleDetailsResponse();
        VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vin);
        if (vehicleInfo == null) {
            return ResultResponse.businessFailed("车辆信息不存在");
        }
        // 查询组织信息
        Map<Long, OrgInfo> orgMap = dataDictService.getOrgMap();
        //查询资产所有人Map
        Map<Integer, DataOwnerInfo> ownerInfoMap = dataDictService.getOwnerMap();

        /*车辆基础信息*/
        VehicleBasicInfoDto basicInfo = new VehicleBasicInfoDto();
        BeanUtil.copyProperties(vehicleInfo, basicInfo);
        if (vehicleInfo.getQuotaAssetCompanyId() != null) {
            DataOwnerInfo dataOwnerInfo = ownerInfoMap.get(vehicleInfo.getQuotaAssetCompanyId());
            if (dataOwnerInfo != null) {
                basicInfo.setQuotaAssetCompanyName(dataOwnerInfo.getName());
            }
        }
        response.setBasicInfo(basicInfo);

        /*查询运营信息*/
        VehicleOperationInfoDto operationInfo = new VehicleOperationInfoDto();
        BeanUtil.copyProperties(vehicleInfo, operationInfo);
        if (vehicleInfo.getAssetCompanyId() != null) {
            operationInfo.setAssetCompanyId(vehicleInfo.getAssetCompanyId());
            DataOwnerInfo dataOwnerInfo = ownerInfoMap.get(vehicleInfo.getAssetCompanyId());
            if (dataOwnerInfo != null) {
                operationInfo.setAssetCompanyName(dataOwnerInfo.getName());
            }
        }
        if (vehicleInfo.getOwnOrganizationId() != null) {
            operationInfo.setOwnOrganizationId(vehicleInfo.getOwnOrganizationId());
            OrgInfo orgInfo = orgMap.get(vehicleInfo.getOwnOrganizationId());
            if (orgInfo != null) {
                operationInfo.setOwnOrganizationName(orgInfo.getCompanyName());
            }
        }
        if (vehicleInfo.getUsageOrganizationId() != null) {
            operationInfo.setUsageOrganizationId(vehicleInfo.getUsageOrganizationId());
            OrgInfo orgInfo = orgMap.get(vehicleInfo.getUsageOrganizationId());
            if (orgInfo != null) {
                operationInfo.setUsageOrganizationName(orgInfo.getCompanyName());
            }
        }
        operationInfo.setRealRetirementDate(vehicleInfo.getRealRetirementDate());
        response.setOperationInfo(operationInfo);

        /*车辆证照信息*/
        VehicleLicenseInfoDto licenseInfo = new VehicleLicenseInfoDto();
        BeanUtil.copyProperties(vehicleInfo, licenseInfo);
        String vehicleLicenseUrl = vehicleInfo.getVehicleLicenseUrl();
        if (StringUtils.isNotBlank(vehicleLicenseUrl)) {
            licenseInfo.setVehicleLicenseUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, vehicleLicenseUrl));
        }
        String certificateOwnershipUrl = vehicleInfo.getCertificateOwnershipUrl();
        if (StringUtils.isNotBlank(certificateOwnershipUrl)) {
            licenseInfo.setCertificateOwnershipUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, certificateOwnershipUrl));
        }
        String certificateConformityUrl = vehicleInfo.getCertificateConformityUrl();
        if (StringUtils.isNotBlank(certificateConformityUrl)) {
            licenseInfo.setCertificateConformityUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, certificateConformityUrl));
        }
        String vehicleInvoiceUrl = vehicleInfo.getVehicleInvoiceUrl();
        if (StringUtils.isNotBlank(vehicleInvoiceUrl)) {
            licenseInfo.setVehicleInvoiceUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, vehicleInvoiceUrl));
        }
        String purchaseTaxUrl = vehicleInfo.getPurchaseTaxUrl();
        if (StringUtils.isNotBlank(purchaseTaxUrl)) {
            licenseInfo.setPurchaseTaxUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, purchaseTaxUrl));
        }
        String operatingPermitUrl = vehicleInfo.getOperatingPermitUrl();
        if (StringUtils.isNotBlank(operatingPermitUrl)) {
            licenseInfo.setOperatingPermitUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, operatingPermitUrl));
        }
        response.setLicenseInfo(licenseInfo);

        /*车辆历史信息*/
        VehicleManagementLegacyInfo vehicleManagementLegacyInfo = tableVehicleManagementLegacyService.queryVehicleByVin(vin);
        if (vehicleManagementLegacyInfo != null) {
            VehicleLegacyInfoResponse legacyInfo = BeanUtil.toBean(vehicleManagementLegacyInfo, VehicleLegacyInfoResponse.class);
            if (vehicleManagementLegacyInfo.getOwnerId() != null) {
                DataOwnerInfo dataOwnerInfo = ownerInfoMap.get(vehicleManagementLegacyInfo.getOwnerId());
                if (dataOwnerInfo != null) {
                    legacyInfo.setOwnerName(dataOwnerInfo.getName());
                }
            }
            if (vehicleManagementLegacyInfo.getAssetOwnerId() != null) {
                DataOwnerInfo dataOwnerInfo = ownerInfoMap.get(vehicleManagementLegacyInfo.getAssetOwnerId());
                if (dataOwnerInfo != null) {
                    legacyInfo.setAssetCompanyName(dataOwnerInfo.getName());
                }
            }
            if (vehicleManagementLegacyInfo.getCompanyOwnerId() != null) {
                DataOwnerInfo dataOwnerInfo = ownerInfoMap.get(vehicleManagementLegacyInfo.getCompanyOwnerId());
                if (dataOwnerInfo != null) {
                    legacyInfo.setCompanyOwnerName(dataOwnerInfo.getName());
                }
            }
            legacyInfo.setRealRetirementDate(vehicleInfo.getRealRetirementDate());
            response.setLegacyInfo(legacyInfo);
        }
        /*查询车型信息*/
        VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(vehicleInfo.getVehicleModelId());
        if (vehicleModelInfo != null) {
            VehicleModelInfoResponse modelInfoResponse = new VehicleModelInfoResponse();
            BeanUtils.copyProperties(vehicleModelInfo, modelInfoResponse);
            response.setVehicleModelInfo(modelInfoResponse);
        }

        /*车辆采购信息*/
        VehiclePurchaseReceiptDto purchaseReceiptInfo = tableVehicleOrderReceiptService.queryVehiclePurchaseReceipt(vin);
        if (purchaseReceiptInfo != null) {
            response.setPurchaseReceiptInfo(purchaseReceiptInfo);
        }

        /*车辆处置信息*/
        List<VehicleDisposalDetailListDto> disposalInfo = tableVehicleDisposalService.queryVehicleDisposalList(vin);
        response.setDisposalList(disposalInfo);
        return ResultResponse.success(response);
    }

    @Override
    public ResultResponse<VehicleBasicResponse> getVehicleBasicInfo(String vin) {
        VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vin);
        if (vehicleInfo == null) {
            return ResultResponse.businessFailed("车辆信息不存在");
        }

        VehicleBasicResponse basicInfo = packageVehicleBasicResponse(vehicleInfo);

        return ResultResponse.success(basicInfo);
    }

    @Override
    public List<VehicleListResponse> getVehicleListResponse(List<String> vinList) {
        List<VehicleListResponse> vehicleListResponseList = tableVehicleService.queryVehicleList(vinList);

        return packageVehicleListResponse(vehicleListResponseList);
    }

    /**
     * 包装车辆列表信息
     *
     * @param vehicleListResponseList
     * @return 车辆基本信息
     */
    private List<VehicleListResponse> packageVehicleListResponse(List<VehicleListResponse> vehicleListResponseList) {
        //查询机构映射信息
        Map<Long, String> orgNameMap = orgService.getOrgNameMap();
        //查询所有资产公司数据字典
        Map<Integer, DataOwnerInfo> ownerMap = dataDictService.getOwnerMap();

        if (CollectionUtil.isNotEmpty(vehicleListResponseList)) {
            for (VehicleListResponse vehicleListResponse : vehicleListResponseList) {
                if (null != vehicleListResponse.getAssetCompanyId()) {
                    vehicleListResponse.setAssetCompanyName(ownerMap.get(vehicleListResponse.getAssetCompanyId()).getName());
                }
                if (null != vehicleListResponse.getOwnOrganizationId()) {
                    vehicleListResponse.setOwnOrganizationName(orgNameMap.get(vehicleListResponse.getOwnOrganizationId()));
                }
                if (null != vehicleListResponse.getUsageOrganizationId()) {
                    vehicleListResponse.setUsageOrganizationName(orgNameMap.get(vehicleListResponse.getUsageOrganizationId()));
                }
            }
        }

        return vehicleListResponseList;
    }

    @Override
    public ResultResponse<VehicleBasicResponse> getVehicleBasicInfoByLicensePlate(String licensePlate) {
        VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByLicensePlate(licensePlate);
        if (vehicleInfo == null) {
            return ResultResponse.businessFailed("车辆信息不存在");
        }

        VehicleBasicResponse basicInfo = packageVehicleBasicResponse(vehicleInfo);

        return ResultResponse.success(basicInfo);
    }

    /**
     * 包装车辆基本信息
     *
     * @param vehicleInfo
     * @return 车辆基本信息
     */
    private VehicleBasicResponse packageVehicleBasicResponse(VehicleInfo vehicleInfo) {
        VehicleBasicResponse basicInfo = BeanUtil.copyProperties(vehicleInfo, VehicleBasicResponse.class);
        if (null != basicInfo.getUsageAgeLimit()) {
            basicInfo.setUsageMonthLimit(basicInfo.getUsageAgeLimit() * 12);
        }
        VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(vehicleInfo.getVehicleModelId());
        if (vehicleModelInfo != null) {
            basicInfo.setVehicleModelName(vehicleModelInfo.getVehicleModelName());
            basicInfo.setVehicleAbbreviationId(vehicleModelInfo.getVehicleAbbreviationId());
            basicInfo.setPriceOnAutohome(vehicleModelInfo.getPriceOnAutohome());
            basicInfo.setMsrp(vehicleModelInfo.getPriceOnAutohome());
        }
        if (null != vehicleInfo.getAssetCompanyId()) {
            DataOwnerInfo assetCompany = tableDataOwnerInfoService.queryOwnerInfoById(vehicleInfo.getAssetCompanyId());
            basicInfo.setAssetCompanyName(assetCompany != null ? assetCompany.getName() : "");
        }
        if (null != vehicleInfo.getOwnOrganizationId()) {
            OrgInfo ownOrgInfo = tableOrgInfoService.queryOrgInfoById(vehicleInfo.getOwnOrganizationId());
            basicInfo.setOwnOrganizationName(ownOrgInfo != null ? ownOrgInfo.getCompanyName() : "");
        }
        if (null != vehicleInfo.getUsageOrganizationId()) {
            OrgInfo usageOrgInfo = tableOrgInfoService.queryOrgInfoById(vehicleInfo.getUsageOrganizationId());
            basicInfo.setUsageOrganizationName(usageOrgInfo != null ? usageOrgInfo.getCompanyName() : "");
        }
        if (null != vehicleInfo.getQuotaAssetCompanyId()) {
            DataOwnerInfo quotaAssetCompany = tableDataOwnerInfoService.queryOwnerInfoById(vehicleInfo.getQuotaAssetCompanyId());
            basicInfo.setQuotaAssetCompanyName(quotaAssetCompany != null ? quotaAssetCompany.getName() : "");
        }
        VehicleManagementLegacyInfo vehicleManagementLegacyInfo = tableVehicleManagementLegacyService.queryVehicleByVin(basicInfo.getVin());
        if (null != vehicleManagementLegacyInfo) {
            basicInfo.setStartDate(vehicleManagementLegacyInfo.getStartDate());
            basicInfo.setLicenseDate(vehicleManagementLegacyInfo.getLicenseDate());
            if (null != vehicleManagementLegacyInfo.getLicenseDate()) {
                // 将 Date 转换为 LocalDate
                LocalDate licenseLocalDate = vehicleManagementLegacyInfo.getLicenseDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate currentDate = LocalDate.now(); // 获取当前日期
                // 到填表日已用月数
                basicInfo.setMonthsUsed((int) ChronoUnit.MONTHS.between(licenseLocalDate, currentDate));
            }
        }

        return basicInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse importVehicleDecoration(List<ImportVehicleDecoration> list, TokenUserInfo tokenUserInfo) {
        if (ObjectUtil.isEmpty(list)) {
            throw new ServiceException("导入文件内容不能为空");
        }
        // 遍历列表获取车架号列表
        List<String> vinList = list.stream().map(ImportVehicleDecoration::getVin).distinct().collect(Collectors.toList());
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        Map<String, VehicleInfo> vehicleMap = vehicleInfoList.stream().collect(Collectors.toMap(VehicleInfo::getVin, vehicleInfo -> vehicleInfo));

        // 获取车辆装饰类型数据字典
        DataDictResponse response = dataDictService.queryDataDictByCode("decorationType");
        List<DataDictDto<Integer>> dataDictList = response.getDataDictList();
        Map<String, DataDictDto<Integer>> dataDictMap = dataDictList.stream().collect(Collectors.toMap(DataDictDto::getName, v -> v));

        //list 按照车架号分组
        Map<String, List<ImportVehicleDecoration>> decorationMap = list.stream().collect(Collectors.groupingBy(ImportVehicleDecoration::getVin));
        for (Map.Entry<String, List<ImportVehicleDecoration>> entry : decorationMap.entrySet()) {
            String vin = entry.getKey();
            VehicleInfo vehicleInfo = vehicleMap.get(vin);
            if (vehicleInfo == null) {
                throw new ServiceException("车架号【" + vin + "】不存在");
            }
            List<ImportVehicleDecoration> decorationList = entry.getValue();
            for (ImportVehicleDecoration decoration : decorationList) {
                String decorationType = decoration.getDecorationTypeDesc();
                if (dataDictMap.get(decorationType) == null) {
                    throw new ServiceException("装饰类型【" + decorationType + "】不存在");
                }
            }
            // 删除
            tableVehicleDecorationService.deleteByVin(vin);
            // 保存数据库
            for (ImportVehicleDecoration decoration : decorationList) {
                VehicleDecoration vehicleDecoration = BeanUtil.copyProperties(decoration, VehicleDecoration.class);
                vehicleDecoration.setDecorationType(dataDictMap.get(decoration.getDecorationTypeDesc()).getValue());
                tableVehicleDecorationService.insert(vehicleDecoration, tokenUserInfo);
            }
            tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, "批量导入装潢信息", tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse importVehicleOtherInfo(List<ImportVehicleOtherInfo> list, TokenUserInfo tokenUserInfo) {
        if (ObjectUtil.isEmpty(list)) {
            throw new ServiceException("导入文件内容不能为空");
        }
        // 遍历列表获取车架号列表
        List<String> vinList = list.stream().map(ImportVehicleOtherInfo::getVin).distinct().collect(Collectors.toList());
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        Map<String, VehicleInfo> vehicleMap = vehicleInfoList.stream().collect(Collectors.toMap(VehicleInfo::getVin, vehicleInfo -> vehicleInfo));

        List<VehicleManagementLegacyInfo> legacyList = tableVehicleManagementLegacyService.queryVehicleByVinList(vinList);
        Map<String, VehicleManagementLegacyInfo> legacyMap = legacyList.stream().collect(Collectors.toMap(VehicleManagementLegacyInfo::getVin, vehicleManagementLegacyInfo -> vehicleManagementLegacyInfo));

        // 查询区域列表
        DataDictResponse<Integer> areaResponse = dataDictService.queryDataMaintainDict("area");
        List<DataDictDto<Integer>> areatList = areaResponse.getDataDictList();
        Map<String, DataDictDto<Integer>> areaMap = areatList.stream().collect(Collectors.toMap(DataDictDto::getName, dataDictDto -> dataDictDto));

        // 查询是否营运/营运类别/号牌种类/车辆拥有公司/查询组织架构
        List<String> systemCodeList = Arrays.asList(
                DataDictEnum.OPERATE_TYPE.getValue(),
                DataDictEnum.OPERATION_CATEGORY.getValue(),
                DataDictEnum.VEHICLE_CATEGORY.getValue(),
                DataDictEnum.CHECK_ORGANIZATION.getValue());
        Map<String, Map<String, Integer>> dataDictMap = dataDictService.getDataMaintainDictValueMap(systemCodeList);
        Map<String, Integer> operateTypeMap = dataDictMap.get(DataDictEnum.OPERATE_TYPE.getValue());
        Map<String, Integer> operationCategoryMap = dataDictMap.get(DataDictEnum.OPERATION_CATEGORY.getValue());
        Map<String, Integer> vehicleCategoryMap = dataDictMap.get(DataDictEnum.VEHICLE_CATEGORY.getValue());
        Map<String, Integer> orgMap = dataDictMap.get(DataDictEnum.CHECK_ORGANIZATION.getValue());

        /*导入参数校验*/
        for (ImportVehicleOtherInfo info : list) {
            VehicleInfo vehicleInfo = vehicleMap.get(info.getVin());
            if (vehicleInfo == null) {
                throw new ServiceException(StrUtil.format("{}-车辆信息不存在", info.getVin()));
            }
            VehicleManagementLegacyInfo legacyInfo = legacyMap.get(info.getVin());
            if (legacyInfo == null) {
                throw new ServiceException(StrUtil.format("{}-车辆信息不存在", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getPropertyStatus())) {
                if (PropertyStatusEnum.getCode(info.getPropertyStatus()) == null) {
                    throw new ServiceException(StrUtil.format("{}-资产状态参数错误", info.getVin()));
                }
            }
            if (StringUtils.isNotBlank(info.getProductLine())) {
                if (ProductLineEnum.getCode(info.getProductLine()) == null) {
                    throw new ServiceException(StrUtil.format("{}-条线参数错误", info.getVin()));
                }
            }
            if (StringUtils.isNotBlank(info.getBusinessLine())) {
                if (BusinessLineEnum.getCode(info.getBusinessLine()) == null) {
                    throw new ServiceException(StrUtil.format("{}-业务线参数错误", info.getVin()));
                }
            }
            if (operateTypeMap.get(info.getIsOperating()) == null) {
                throw new ServiceException(StrUtil.format("{}-是否营运参数错误", info.getVin()));
            }
            if (operationCategoryMap.get(info.getOperationCategory()) == null) {
                throw new ServiceException(StrUtil.format("{}-营运类别参数错误", info.getVin()));
            }
            if (vehicleCategoryMap.get(info.getLicensePlateType()) == null) {
                throw new ServiceException(StrUtil.format("{}-号牌种类参数错误", info.getVin()));
            }
            if (areaMap.get(info.getJurisdictionalDistrict()) == null) {
                throw new ServiceException(StrUtil.format("{}-管辖区域参数错误", info.getVin()));
            }
            if (orgMap.get(info.getOwnOrganizationName()) == null) {
                throw new ServiceException(StrUtil.format("{}-实际运营公司（所属）参数错误", info.getVin()));
            }
            if (orgMap.get(info.getUsageOrganizationName()) == null) {
                throw new ServiceException(StrUtil.format("{}-实际运营公司（使用）参数错误", info.getVin()));
            }
        }

        // 更新车辆信息
        for (ImportVehicleOtherInfo info : list) {
            VehicleInfo vehicleInfo = vehicleMap.get(info.getVin());
            vehicleInfo.setAreaId(areaMap.get(info.getJurisdictionalDistrict()).getValue());
            vehicleInfo.setProductDate(DateTimeUtils.stringToDate(info.getVehicleManufacturingDate(), DateTimeUtils.DATE_TYPE3));
            vehicleInfo.setIssuanceDate(DateTimeUtils.stringToDate(info.getIssuanceDate(), DateTimeUtils.DATE_TYPE3));
            vehicleInfo.setCertificateNumber(info.getCertificateNumber());
            vehicleInfo.setOwnOrganizationId(Long.valueOf(orgMap.get(info.getOwnOrganizationName())));
            vehicleInfo.setUsageOrganizationId(Long.valueOf(orgMap.get(info.getUsageOrganizationName())));
            vehicleInfo.setUsageAgeLimit(Integer.parseInt(info.getUsageYears()));
            vehicleInfo.setDepreciationAgeLimit(Integer.parseInt(info.getDepreciationYears()));
            vehicleInfo.setPurchasePrice(info.getNakedCarPrice());
            vehicleInfo.setPurchaseTax(info.getPurchaseTax());
            vehicleInfo.setLicensePlatePrice(info.getLicensePlateFee());
            vehicleInfo.setLicensePlateOtherPrice(info.getRegistrationMiscellaneousFee());
            vehicleInfo.setUpholsterPrice(info.getDecorationFee());
            vehicleInfo.setTotalPrice(info.getPurchaseTotalPrice());
            if (StringUtils.isNotBlank(info.getPropertyStatus()) &&
                    PropertyStatusEnum.getCode(info.getPropertyStatus()) != null) {
                vehicleInfo.setPropertyStatus(PropertyStatusEnum.getCode(info.getPropertyStatus()));
            }
            if (StringUtils.isNotBlank(info.getProductLine()) &&
                    ProductLineEnum.getCode(info.getProductLine()) != null) {
                vehicleInfo.setProductLine(ProductLineEnum.getCode(info.getProductLine()));
            }
            if (StringUtils.isNotBlank(info.getBusinessLine()) &&
                    BusinessLineEnum.getCode(info.getBusinessLine()) != null) {
                vehicleInfo.setBusinessLine(BusinessLineEnum.getCode(info.getBusinessLine()));
            }
            tableVehicleService.updateVehicle(vehicleInfo, tokenUserInfo);

            VehicleManagementLegacyInfo legacyInfo = legacyMap.get(info.getVin());
            legacyInfo.setOperateTypeId(operateTypeMap.get(info.getIsOperating()));
            legacyInfo.setOperationCategoryId(operationCategoryMap.get(info.getOperationCategory()));
            legacyInfo.setVehicleCategoryId(vehicleCategoryMap.get(info.getLicensePlateType()));
            legacyInfo.setOperatingNo(info.getOperationCertificateNumber());
            tableVehicleManagementLegacyService.updateSelectiveById(legacyInfo, tokenUserInfo);
            // 操作日志
            tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, "批量导入车辆其他信息", tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    public ResultResponse importVehicleDepreciationInfo(List<ImportVehicleDepreciationInfo> list, TokenUserInfo tokenUserInfo) {
        if (ObjectUtil.isEmpty(list)) {
            throw new ServiceException("导入文件内容不能为空");
        }
        // 校验导入参数
        list.forEach(item -> ValidationUtils.validate(item));

        // 遍历列表获取车架号列表
        List<String> vinList = list.stream().map(ImportVehicleDepreciationInfo::getVin).distinct().collect(Collectors.toList());
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        Map<String, VehicleInfo> vehicleMap = vehicleInfoList.stream().collect(Collectors.toMap(VehicleInfo::getVin, vehicleInfo -> vehicleInfo));

        list.forEach(importAnnualInspectionExpiryDate -> {
            VehicleInfo vehicleInfo = vehicleMap.get(importAnnualInspectionExpiryDate.getVin());
            if (vehicleInfo == null) {
                throw new ServiceException(StrUtil.format("{}-车辆信息不存在", importAnnualInspectionExpiryDate.getVin()));
            }
            VehicleDepreciationData vehicleDepreciationData = new VehicleDepreciationData();
            vehicleDepreciationData.setVin(importAnnualInspectionExpiryDate.getVin());
            vehicleDepreciationData.setCurrentMonth(importAnnualInspectionExpiryDate.getCurrentMonth());
            vehicleDepreciationData.setOriginalAmount(importAnnualInspectionExpiryDate.getOriginalAmount());
            vehicleDepreciationData.setDepreciationMonths(Integer.valueOf(importAnnualInspectionExpiryDate.getDepreciationMonths()));
            vehicleDepreciationData.setDepreciationStartDate(DateTimeUtils.stringToDate(importAnnualInspectionExpiryDate.getDepreciationStartDate(), DateTimeUtils.DATE_TYPE3));
            vehicleDepreciationData.setAccumulatedDepreciationMonths(Integer.valueOf(importAnnualInspectionExpiryDate.getAccumulatedDepreciationMonths()));
            vehicleDepreciationData.setDepreciationAmount(importAnnualInspectionExpiryDate.getDepreciationAmount());
            vehicleDepreciationData.setRemainingResidualValue(importAnnualInspectionExpiryDate.getRemainingResidualValue());
            tableVehicleDepreciationDataService.insert(vehicleDepreciationData, tokenUserInfo);
            vehicleInfo.setDepreciationDataId(vehicleDepreciationData.getId());
            tableVehicleService.updateVehicle(vehicleInfo, tokenUserInfo);
            // 保存日志
            tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, "批量导入车辆折旧信息", tokenUserInfo);
        });
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse importAnnualInspectionExpiryDate(List<ImportAnnualInspectionExpiryDate> list, TokenUserInfo tokenUserInfo) {
        if (ObjectUtil.isEmpty(list)) {
            throw new ServiceException("上传文件内容不能为空");
        }
        // 遍历列表获取车架号列表
        List<String> vinList = list.stream().map(ImportAnnualInspectionExpiryDate::getVin).distinct().collect(Collectors.toList());
        if (vinList.size() != list.size()) {
            throw new ServiceException("有重复车架号信息");
        }
        // 查询车辆列表
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        Map<String, VehicleInfo> vehicleMap = vehicleInfoList.stream().collect(Collectors.toMap(VehicleInfo::getVin, vehicleInfo -> vehicleInfo));
        list.forEach(importAnnualInspectionExpiryDate -> {
            VehicleInfo vehicleInfo = vehicleMap.get(importAnnualInspectionExpiryDate.getVin());
            if (vehicleInfo == null) {
                throw new ServiceException(StrUtil.format("{}-车辆信息不存在", importAnnualInspectionExpiryDate.getVin()));
            }
            vehicleInfo.setAnnualInspectionDueDateRegistrationCard(DateTimeUtils.stringToDate(importAnnualInspectionExpiryDate.getAnnualInspectionExpiryDate(), DateTimeUtils.DATE_TYPE3));
            tableVehicleService.updateVehicle(vehicleInfo, tokenUserInfo);

            tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, "批量导入车辆登记年检到期日", tokenUserInfo);
        });
        return ResultResponse.success();
    }

    @Override
    public ResultResponse importVehicleMasterDate(List<ImportVehicleMasterData> list, TokenUserInfo tokenUserInfo) {
        if (ObjectUtil.isEmpty(list)) {
            throw new ServiceException("导入文件内容不能为空");
        }

        List<String> vinList = list.stream().map(ImportVehicleMasterData::getVin).distinct().collect(Collectors.toList());
        if (vinList.size() != list.size()) {
            throw new ServiceException("有重复车架号信息");
        }
        List<String> licensePlateList = list.stream().map(ImportVehicleMasterData::getLicensePlate).distinct().collect(Collectors.toList());
        if (licensePlateList.size() != list.size()) {
            throw new ServiceException("有重复车牌号信息");
        }
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        if (CollectionUtil.isNotEmpty(vehicleInfoList)) {
            throw new ServiceException(StrUtil.format("{}-车架号已存在", vehicleInfoList.get(0).getVin()));
        }

        List<VehicleInfo> vehiclePlateList = tableVehicleService.queryVehicleByLicensePlateList(licensePlateList);
        if (CollectionUtil.isNotEmpty(vehiclePlateList)) {
            throw new ServiceException(StrUtil.format("{}-车牌号已存在", vehiclePlateList.get(0).getLicensePlate()));
        }

        // 查询区域列表
        DataDictResponse<Integer> areaResponse = dataDictService.queryDataMaintainDict("area");
        List<DataDictDto<Integer>> areatList = areaResponse.getDataDictList();
        Map<String, DataDictDto<Integer>> areaMap = areatList.stream().collect(Collectors.toMap(DataDictDto::getName, dataDictDto -> dataDictDto));
        // 车型列表
        Map<String, VehicleModelInfo> vehicleModelInfoMap = tableVehicleModelInfoService.getAllVehicleModelMapByName();

        // 查询是否营运/营运类别/号牌种类/车身颜色/合同类型/获取方式/使用性质/车辆拥有公司/供应商/组织机构
        List<String> systemCodeList = Arrays.asList(
                DataDictEnum.OPERATE_TYPE.getValue(),
                DataDictEnum.OPERATION_CATEGORY.getValue(),
                DataDictEnum.VEHICLE_CATEGORY.getValue(),
                DataDictEnum.VEHICLE_COLOR.getValue(),
                DataDictEnum.CONTRACT_TYPE.getValue(),
                DataDictEnum.OBTAIN_WAY.getValue(),
                DataDictEnum.USAGE.getValue(),
                DataDictEnum.OWNER.getValue(),
                DataDictEnum.SUPPLIER.getValue(),
                DataDictEnum.CHECK_ORGANIZATION.getValue());
        Map<String, Map<String, Integer>> dataDictMap = dataDictService.getDataMaintainDictValueMap(systemCodeList);
        Map<String, Integer> operateTypeMap = dataDictMap.get(DataDictEnum.OPERATE_TYPE.getValue());
        Map<String, Integer> operationCategoryMap = dataDictMap.get(DataDictEnum.OPERATION_CATEGORY.getValue());
        Map<String, Integer> vehicleCategoryMap = dataDictMap.get(DataDictEnum.VEHICLE_CATEGORY.getValue());
        Map<String, Integer> vehicleColorMap = dataDictMap.get(DataDictEnum.VEHICLE_COLOR.getValue());
        Map<String, Integer> contractTypeMap = dataDictMap.get(DataDictEnum.CONTRACT_TYPE.getValue());
        Map<String, Integer> obtainWayMap = dataDictMap.get(DataDictEnum.OBTAIN_WAY.getValue());
        Map<String, Integer> usageMap = dataDictMap.get(DataDictEnum.USAGE.getValue());
        Map<String, Integer> ownerMap = dataDictMap.get(DataDictEnum.OWNER.getValue());
        Map<String, Integer> supplierMap = dataDictMap.get(DataDictEnum.SUPPLIER.getValue());
        Map<String, Integer> orgMap = dataDictMap.get(DataDictEnum.CHECK_ORGANIZATION.getValue());

        /*参数校验*/
        for (ImportVehicleMasterData importVehicleMasterData : list) {
            if (StringUtils.isNotBlank(importVehicleMasterData.getIsOperating()) &&
                    operateTypeMap.get(importVehicleMasterData.getIsOperating()) == null) {
                throw new ServiceException(StrUtil.format("{}-是否营运参数错误", importVehicleMasterData.getVin()));
            }
            if (StringUtils.isNotBlank(importVehicleMasterData.getOperationCategory()) &&
                    operationCategoryMap.get(importVehicleMasterData.getOperationCategory()) == null) {
                throw new ServiceException(StrUtil.format("{}-营运类别参数错误", importVehicleMasterData.getVin()));
            }
            if (StringUtils.isNotBlank(importVehicleMasterData.getLicensePlateType()) &&
                    vehicleCategoryMap.get(importVehicleMasterData.getLicensePlateType()) == null) {
                throw new ServiceException(StrUtil.format("{}-号牌种类参数错误", importVehicleMasterData.getVin()));
            }
            if (StringUtils.isNotBlank(importVehicleMasterData.getVehicleBodyColor()) &&
                    vehicleColorMap.get(importVehicleMasterData.getVehicleBodyColor()) == null) {
                throw new ServiceException(StrUtil.format("{}-车身颜色参数错误", importVehicleMasterData.getVin()));
            }
            if (StringUtils.isNotBlank(importVehicleMasterData.getJurisdictionalDistrict()) &&
                    areaMap.get(importVehicleMasterData.getJurisdictionalDistrict()) == null) {
                throw new ServiceException(StrUtil.format("{}-管辖区域参数错误", importVehicleMasterData.getVin()));
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getContractType())) {
                if (contractTypeMap.get(importVehicleMasterData.getContractType()) == null) {
                    throw new ServiceException(StrUtil.format("{}-合同形式参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getVehicleModelName())) {
                if (vehicleModelInfoMap.get(importVehicleMasterData.getVehicleModelName()) == null) {
                    throw new ServiceException(StrUtil.format("{}-车型名称参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getObtainWay())) {
                if (obtainWayMap.get(importVehicleMasterData.getObtainWay()) == null) {
                    throw new ServiceException(StrUtil.format("{}-获得方式参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getUsageIdRegistrationCard())) {
                if (usageMap.get(importVehicleMasterData.getUsageIdRegistrationCard()) == null) {
                    throw new ServiceException(StrUtil.format("{}-使用性质参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getSupplier())) {
                if (supplierMap.get(importVehicleMasterData.getSupplier()) == null) {
                    throw new ServiceException(StrUtil.format("{}-供应商参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getOwner())) {
                if (ownerMap.get(importVehicleMasterData.getOwner()) == null) {
                    throw new ServiceException(StrUtil.format("{}-机动车所有者参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getOwnerName())) {
                if (ownerMap.get(importVehicleMasterData.getOwnerName()) == null) {
                    throw new ServiceException(StrUtil.format("{}-所有人参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getOwnOrganizationName())) {
                if (orgMap.get(importVehicleMasterData.getOwnOrganizationName()) == null) {
                    throw new ServiceException(StrUtil.format("{}-资产机构参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getUsageOrganizationName())) {
                if (orgMap.get(importVehicleMasterData.getUsageOrganizationName()) == null) {
                    throw new ServiceException(StrUtil.format("{}-使用机构参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getHasRight())) {
                PublicBooleanStatusEnum publicBooleanStatusEnum = PublicBooleanStatusEnum.getEnum(importVehicleMasterData.getHasRight());
                if (publicBooleanStatusEnum == null) {
                    throw new ServiceException(StrUtil.format("{}-是否有产权证参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (StringUtils.isNotBlank(importVehicleMasterData.getProductLine())) {
                if (ProductLineEnum.getCode(importVehicleMasterData.getProductLine()) == null) {
                    throw new ServiceException(StrUtil.format("{}-条线参数错误", importVehicleMasterData.getVin()));
                }
            }
            if (StringUtils.isNotBlank(importVehicleMasterData.getBusinessLine())) {
                if (BusinessLineEnum.getCode(importVehicleMasterData.getBusinessLine()) == null) {
                    throw new ServiceException(StrUtil.format("{}-业务类型参数错误", importVehicleMasterData.getVin()));
                }
            }
        }

        /*批量保存入库*/
        for (ImportVehicleMasterData importVehicleMasterData : list) {
            VehicleInfo vehicleInfo = new VehicleInfo();
            String assetNumber = redisUtils.generateUniqueId("ZC");
            vehicleInfo.setVehicleAssetId(assetNumber);
            vehicleInfo.setVin(importVehicleMasterData.getVin());
            vehicleInfo.setPropertyStatus(PropertyStatusEnum.FIXED_ASSET.getCode());
            if (StringUtils.isNotBlank(importVehicleMasterData.getEngineNo())){
                vehicleInfo.setEngineNo(importVehicleMasterData.getEngineNo());
            }
            if (StringUtils.isNotBlank(importVehicleMasterData.getLicensePlate())){
                vehicleInfo.setLicensePlate(importVehicleMasterData.getLicensePlate());
            }
            if (importVehicleMasterData.getPurchasePrice() != null){
                vehicleInfo.setPurchasePrice(importVehicleMasterData.getPurchasePrice());
            }

            if (importVehicleMasterData.getPurchaseTax() != null){
                vehicleInfo.setPurchaseTax(importVehicleMasterData.getPurchaseTax());
            }

            if (importVehicleMasterData.getLicensePlateFee() != null){
                vehicleInfo.setLicensePlatePrice(importVehicleMasterData.getLicensePlateFee());
            }

            if (importVehicleMasterData.getRegistrationMiscellaneousFee() != null){
                vehicleInfo.setLicensePlateOtherPrice(importVehicleMasterData.getRegistrationMiscellaneousFee());
            }

            if (importVehicleMasterData.getDecorationFee() != null){
                vehicleInfo.setUpholsterPrice(importVehicleMasterData.getDecorationFee());
            }

            if (importVehicleMasterData.getPurchaseTotalPrice() != null){
                vehicleInfo.setTotalPrice(importVehicleMasterData.getPurchaseTotalPrice());
            }


            //车身颜色
            if (StringUtils.isNotBlank(importVehicleMasterData.getVehicleBodyColor())) {
                if (vehicleColorMap.get(importVehicleMasterData.getVehicleBodyColor()) != null) {
                    vehicleInfo.setVehicleColorId(vehicleColorMap.get(importVehicleMasterData.getVehicleBodyColor()));
                }
            }
            //资产机构
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getOwnOrganizationName())) {
                if (orgMap.get(importVehicleMasterData.getOwnOrganizationName()) != null) {
                    vehicleInfo.setOwnOrganizationId(Long.valueOf(orgMap.get(importVehicleMasterData.getOwnOrganizationName())));
                }
            }
            //使用机构
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getUsageOrganizationName())) {
                if (orgMap.get(importVehicleMasterData.getUsageOrganizationName()) != null) {
                    vehicleInfo.setUsageOrganizationId(Long.valueOf(orgMap.get(importVehicleMasterData.getUsageOrganizationName())));
                }
            }
            //使用年限
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getUsageYears())) {
                vehicleInfo.setUsageAgeLimit(Integer.valueOf(importVehicleMasterData.getUsageYears()));
            }
            //折旧年限
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getDepreciationYears())) {
                vehicleInfo.setDepreciationAgeLimit(Integer.valueOf(importVehicleMasterData.getDepreciationYears()));
            }
            //车型
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getVehicleModelName())) {
                if (vehicleModelInfoMap.get(importVehicleMasterData.getVehicleModelName()) != null) {
                    vehicleInfo.setVehicleModelId(vehicleModelInfoMap.get(importVehicleMasterData.getVehicleModelName()).getId());
                }
            }
            //获得方式
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getObtainWay())) {
                if (obtainWayMap.get(importVehicleMasterData.getObtainWay()) != null) {
                    vehicleInfo.setObtainWayId(obtainWayMap.get(importVehicleMasterData.getObtainWay()));
                }
            }
            //使用性质
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getUsageIdRegistrationCard())) {
                if (usageMap.get(importVehicleMasterData.getUsageIdRegistrationCard()) != null) {
                    vehicleInfo.setUsageIdRegistrationCard(usageMap.get(importVehicleMasterData.getUsageIdRegistrationCard()));
                }
            }
            //管辖区县
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getJurisdictionalDistrict())) {
                if (areaMap.get(importVehicleMasterData.getJurisdictionalDistrict()) == null) {
                    vehicleInfo.setAreaId(areaMap.get(importVehicleMasterData.getJurisdictionalDistrict()).getValue());
                }
            }
            // 档案编号
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getFileNumber())) {
                vehicleInfo.setFileNumber(importVehicleMasterData.getFileNumber());
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getProductDate())) {
                vehicleInfo.setProductDate(DateTimeUtils.stringToDate(importVehicleMasterData.getProductDate(), DateTimeUtils.DATE_TYPE3));
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getVehicleRegisterNo())) {
                vehicleInfo.setCertificateNumber(importVehicleMasterData.getVehicleRegisterNo());
            }

            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getRetirementDate())) {
                vehicleInfo.setRetirementDateRegistrationCard(DateTimeUtils.stringToDate(importVehicleMasterData.getRetirementDate(), DateTimeUtils.DATE_TYPE3));
            }

            //供应商
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getSupplier())) {
                if (supplierMap.get(importVehicleMasterData.getSupplier()) != null) {
                    vehicleInfo.setSupplierId(supplierMap.get(importVehicleMasterData.getSupplier()));
                }
            }
            // 资产所属公司
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getOwner())) {
                if (ownerMap.get(importVehicleMasterData.getOwner()) != null) {
                    vehicleInfo.setAssetCompanyId(ownerMap.get(importVehicleMasterData.getOwner()));
                }
            }
            if (StringUtils.isNotBlank(importVehicleMasterData.getProductLine()) &&
                    ProductLineEnum.getCode(importVehicleMasterData.getProductLine()) != null) {
                vehicleInfo.setProductLine(ProductLineEnum.getCode(importVehicleMasterData.getProductLine()));
            }
            if (StringUtils.isNotBlank(importVehicleMasterData.getBusinessLine()) &&
                    BusinessLineEnum.getCode(importVehicleMasterData.getBusinessLine()) != null) {
                vehicleInfo.setBusinessLine(BusinessLineEnum.getCode(importVehicleMasterData.getBusinessLine()));
            }
            tableVehicleService.saveVehicle(vehicleInfo, tokenUserInfo);
            // 保存老车管数据
            VehicleManagementLegacyInfo managementLegacyInfo = new VehicleManagementLegacyInfo();
            managementLegacyInfo.setVin(importVehicleMasterData.getVin());

            if (StringUtils.isNotBlank(importVehicleMasterData.getIsOperating()) &&
                    operateTypeMap.get(importVehicleMasterData.getIsOperating()) != null) {
                managementLegacyInfo.setOperateTypeId(operateTypeMap.get(importVehicleMasterData.getIsOperating()));
            }

            if (StringUtils.isNotBlank(importVehicleMasterData.getLicensePlateType()) &&
                    vehicleCategoryMap.get(importVehicleMasterData.getLicensePlateType()) != null) {
                managementLegacyInfo.setVehicleCategoryId(vehicleCategoryMap.get(importVehicleMasterData.getLicensePlateType()));
            }

            if (StringUtils.isNotBlank(importVehicleMasterData.getOperationCategory()) &&
                    operationCategoryMap.get(importVehicleMasterData.getOperationCategory()) != null) {
                managementLegacyInfo.setOperationCategoryId(operationCategoryMap.get(importVehicleMasterData.getOperationCategory()));
            }

            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getContractType())) {
                if (contractTypeMap.get(importVehicleMasterData.getContractType()) != null) {
                    managementLegacyInfo.setContractTypeId(contractTypeMap.get(importVehicleMasterData.getContractType()));
                }
            }
            // 营运证号
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getOperationCertificateNumber())) {
                managementLegacyInfo.setOperatingNo(importVehicleMasterData.getOperationCertificateNumber());
            }
            // 是否有产权证
            managementLegacyInfo.setHasRight(-1);
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getHasRight())) {
                PublicBooleanStatusEnum publicBooleanStatusEnum = PublicBooleanStatusEnum.getEnum(importVehicleMasterData.getHasRight());
                if (publicBooleanStatusEnum != null) {
                    managementLegacyInfo.setHasRight(publicBooleanStatusEnum.getCode());
                }
            }
            // 机动车所有者
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getOwner())) {
                if (ownerMap.get(importVehicleMasterData.getOwner()) != null) {
                    managementLegacyInfo.setOwnerId(ownerMap.get(importVehicleMasterData.getOwner()));
                }
            }
            // 资产所有
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getOwnerName())) {
                if (ownerMap.get(importVehicleMasterData.getOwnerName()) != null) {
                    managementLegacyInfo.setAssetOwnerId(ownerMap.get(importVehicleMasterData.getOwnerName()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getCompanyName())) {
                if (ownerMap.get(importVehicleMasterData.getCompanyName()) != null) {
                    managementLegacyInfo.setCompanyOwnerId(ownerMap.get(importVehicleMasterData.getCompanyName()));
                }
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getStartDate())) {
                managementLegacyInfo.setStartDate(DateTimeUtils.stringToDate(importVehicleMasterData.getStartDate(), DateTimeUtils.DATE_TYPE3));
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getLicenseDate())) {
                managementLegacyInfo.setLicenseDate(DateTimeUtils.stringToDate(importVehicleMasterData.getLicenseDate(), DateTimeUtils.DATE_TYPE3));
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getPurchaseDate())) {
                managementLegacyInfo.setPurchaseDate(DateTimeUtils.stringToDate(importVehicleMasterData.getPurchaseDate(), DateTimeUtils.DATE_TYPE3));
            }
            if (ObjectUtil.isNotEmpty(importVehicleMasterData.getOperationStartDate())) {
                managementLegacyInfo.setOperationStartDate(DateTimeUtils.stringToDate(importVehicleMasterData.getOperationStartDate(), DateTimeUtils.DATE_TYPE3));
            }
            tableVehicleManagementLegacyService.insert(managementLegacyInfo, tokenUserInfo);
            tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, "批量导入车辆信息", tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    public ResultResponse importVehicleDataInfo(List<ImportAdminVehicleOtherInfo> list, TokenUserInfo tokenUserInfo) {
        if (ObjectUtil.isEmpty(list)) {
            throw new ServiceException("导入文件内容不能为空");
        }
        // 遍历列表获取车架号列表
        List<String> vinList = list.stream().map(ImportAdminVehicleOtherInfo::getVin).distinct().collect(Collectors.toList());
        SearchAssetVehicleRequest request = new SearchAssetVehicleRequest();
        request.setVinList(vinList);
        List<VehicleListResponse> vehicleListResponses = tableVehicleService.searchAssetVehicleList(request, tokenUserInfo);
        if (CollectionUtil.isEmpty(vehicleListResponses)){
            throw new ServiceException("导入的车辆没有权限修改");
        }
        Map<String, VehicleListResponse> vehicleInfoMap = vehicleListResponses.stream().collect(Collectors.toMap(VehicleListResponse::getVin, vehicleInfo -> vehicleInfo));

        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        Map<String, VehicleInfo> vehicleMap = vehicleInfoList.stream().collect(Collectors.toMap(VehicleInfo::getVin, vehicleInfo -> vehicleInfo));

        List<VehicleManagementLegacyInfo> legacyList = tableVehicleManagementLegacyService.queryVehicleByVinList(vinList);
        Map<String, VehicleManagementLegacyInfo> legacyMap = legacyList.stream().collect(Collectors.toMap(VehicleManagementLegacyInfo::getVin, vehicleManagementLegacyInfo -> vehicleManagementLegacyInfo));

        // 查询区域列表
        DataDictResponse<Integer> areaResponse = dataDictService.queryDataMaintainDict("area");
        List<DataDictDto<Integer>> areatList = areaResponse.getDataDictList();
        Map<String, DataDictDto<Integer>> areaMap = areatList.stream().collect(Collectors.toMap(DataDictDto::getName, dataDictDto -> dataDictDto));

        // 查询是否营运/营运类别/号牌种类/车辆拥有公司/查询组织架构
        List<String> systemCodeList = Arrays.asList(
                DataDictEnum.OPERATE_TYPE.getValue(),
                DataDictEnum.OPERATION_CATEGORY.getValue(),
                DataDictEnum.VEHICLE_CATEGORY.getValue(),
                DataDictEnum.VEHICLE_COLOR.getValue(),
                DataDictEnum.OWNER.getValue(),
                DataDictEnum.CHECK_ORGANIZATION.getValue());
        Map<String, Map<String, Integer>> dataDictMap = dataDictService.getDataMaintainDictValueMap(systemCodeList);
        Map<String, Integer> operateTypeMap = dataDictMap.get(DataDictEnum.OPERATE_TYPE.getValue());
        Map<String, Integer> operationCategoryMap = dataDictMap.get(DataDictEnum.OPERATION_CATEGORY.getValue());
        Map<String, Integer> vehicleCategoryMap = dataDictMap.get(DataDictEnum.VEHICLE_CATEGORY.getValue());
        Map<String, Integer> ownerMap = dataDictMap.get(DataDictEnum.OWNER.getValue());
        Map<String, Integer> orgMap = dataDictMap.get(DataDictEnum.CHECK_ORGANIZATION.getValue());
        Map<String, Integer> vehicleColorMap = dataDictMap.get(DataDictEnum.VEHICLE_COLOR.getValue());
        // 车型信息
        Map<Long, VehicleModelInfo> vehicleModelMap = tableVehicleModelInfoService.getAllVehicleModelMap();

        /*导入参数校验*/
        for (ImportAdminVehicleOtherInfo info : list) {
            VehicleInfo vehicleInfo = vehicleMap.get(info.getVin());
            if (vehicleInfo == null) {
                throw new ServiceException(StrUtil.format("{}-车辆信息不存在", info.getVin()));
            }
            VehicleManagementLegacyInfo legacyInfo = legacyMap.get(info.getVin());
            if (legacyInfo == null) {
                throw new ServiceException(StrUtil.format("{}-车辆信息不存在", info.getVin()));
            }
            // 校验车辆权限
            VehicleListResponse vehicleListResponse = vehicleInfoMap.get(info.getVin());
            if (vehicleListResponse == null){
                throw new ServiceException(StrUtil.format("{}-车辆没有权限修改", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getPropertyStatus())) {
                if (PropertyStatusEnum.getCode(info.getPropertyStatus()) == null) {
                    throw new ServiceException(StrUtil.format("{}-资产状态参数错误", info.getVin()));
                }
            }
            if (StringUtils.isNotBlank(info.getProductLine()) &&
                    ProductLineEnum.getCode(info.getProductLine()) == null) {
                throw new ServiceException(StrUtil.format("{}-条线参数错误", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getBusinessLine()) &&
                    BusinessLineEnum.getCode(info.getBusinessLine()) == null) {
                throw new ServiceException(StrUtil.format("{}-业务线参数错误", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getIsOperating()) && operateTypeMap.get(info.getIsOperating()) == null) {
                throw new ServiceException(StrUtil.format("{}-是否营运参数错误", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getOperationCategory()) && operationCategoryMap.get(info.getOperationCategory()) == null) {
                throw new ServiceException(StrUtil.format("{}-营运类别参数错误", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getLicensePlateType()) && vehicleCategoryMap.get(info.getLicensePlateType()) == null) {
                throw new ServiceException(StrUtil.format("{}-号牌种类参数错误", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getJurisdictionalDistrict()) && areaMap.get(info.getJurisdictionalDistrict()) == null) {
                throw new ServiceException(StrUtil.format("{}-管辖区域参数错误", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getAssetCompanyName()) && ownerMap.get(info.getAssetCompanyName()) == null) {
                throw new ServiceException(StrUtil.format("{}-资产所属公司参数错误", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getOwnOrganizationName()) && orgMap.get(info.getOwnOrganizationName()) == null) {
                throw new ServiceException(StrUtil.format("{}-实际运营公司（所属）参数错误", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getUsageOrganizationName()) && orgMap.get(info.getUsageOrganizationName()) == null) {
                throw new ServiceException(StrUtil.format("{}-实际运营公司（使用）参数错误", info.getVin()));
            }
            if (info.getVehicleModelId() != null && vehicleModelMap.get(info.getVehicleModelId()) == null){
                throw new ServiceException(StrUtil.format("{}-车型ID不存在", info.getVehicleModelId()));
            }
            if (StringUtils.isNotBlank(info.getVehicleBodyColor()) && vehicleColorMap.get(info.getVehicleBodyColor()) == null){
                throw new ServiceException(StrUtil.format("{}-车身颜色不存在", info.getVehicleBodyColor()));
            }
            if (StringUtils.isNotBlank(info.getEngineModel()) && info.getEngineModel().length() > 32){
                throw new ServiceException(StrUtil.format("{}-发动机型号长度不能超过32个字符", info.getVin()));
            }
            if (StringUtils.isNotBlank(info.getEngineNo()) && info.getEngineNo().length() > 30){
                throw new ServiceException(StrUtil.format("{}-发动机号长度不能超过30个字符", info.getVin()));
            }
        }

        // 更新车辆信息
        for (ImportAdminVehicleOtherInfo info : list) {
            VehicleInfo vehicleInfo = vehicleMap.get(info.getVin());
            vehicleInfo.setProductDate(DateTimeUtils.stringToDate(info.getVehicleManufacturingDate(), DateTimeUtils.DATE_TYPE3));
            vehicleInfo.setIssuanceDate(DateTimeUtils.stringToDate(info.getIssuanceDate(), DateTimeUtils.DATE_TYPE3));
            if (StringUtils.isNotBlank(info.getJurisdictionalDistrict()) && areaMap.get(info.getJurisdictionalDistrict()) != null) {
                vehicleInfo.setAreaId(areaMap.get(info.getJurisdictionalDistrict()).getValue());
            }
            if (StringUtils.isNotBlank(info.getCertificateNumber())){
                vehicleInfo.setCertificateNumber(info.getCertificateNumber());
            }
            if (StringUtils.isNotBlank(info.getAssetCompanyName()) && ownerMap.get(info.getAssetCompanyName()) != null) {
                vehicleInfo.setAssetCompanyId(ownerMap.get(info.getAssetCompanyName()));
            }
            if (StringUtils.isNotBlank(info.getOwnOrganizationName()) && orgMap.get(info.getOwnOrganizationName()) != null) {
                vehicleInfo.setOwnOrganizationId(Long.valueOf(orgMap.get(info.getOwnOrganizationName())));
            }
            if (StringUtils.isNotBlank(info.getUsageOrganizationName()) && orgMap.get(info.getUsageOrganizationName()) != null) {
                vehicleInfo.setUsageOrganizationId(Long.valueOf(orgMap.get(info.getUsageOrganizationName())));
            }
            if (StringUtils.isNotBlank(info.getUsageYears())){
                vehicleInfo.setUsageAgeLimit(Integer.parseInt(info.getUsageYears()));
            }
            if (StringUtils.isNotBlank(info.getDepreciationYears())){
                vehicleInfo.setDepreciationAgeLimit(Integer.parseInt(info.getDepreciationYears()));
            }
            if (info.getNakedCarPrice() != null){
                vehicleInfo.setPurchasePrice(info.getNakedCarPrice());
            }
            if (info.getPurchaseTax() != null){
                vehicleInfo.setPurchaseTax(info.getPurchaseTax());
            }
            if (info.getLicensePlateFee() != null){
                vehicleInfo.setLicensePlatePrice(info.getLicensePlateFee());
            }
            if (info.getRegistrationMiscellaneousFee() != null){
                vehicleInfo.setLicensePlateOtherPrice(info.getRegistrationMiscellaneousFee());
            }
            if (info.getDecorationFee() != null){
                vehicleInfo.setUpholsterPrice(info.getDecorationFee());
            }
            if (info.getPurchaseTotalPrice() != null){
                vehicleInfo.setTotalPrice(info.getPurchaseTotalPrice());
            }
            if (StringUtils.isNotBlank(info.getPropertyStatus()) &&
                    PropertyStatusEnum.getCode(info.getPropertyStatus()) != null) {
                vehicleInfo.setPropertyStatus(PropertyStatusEnum.getCode(info.getPropertyStatus()));
            }
            if (StringUtils.isNotBlank(info.getProductLine()) &&
                    ProductLineEnum.getCode(info.getProductLine()) != null) {
                vehicleInfo.setProductLine(ProductLineEnum.getCode(info.getProductLine()));
            }
            if (StringUtils.isNotBlank(info.getBusinessLine()) &&
                    BusinessLineEnum.getCode(info.getBusinessLine()) != null) {
                vehicleInfo.setBusinessLine(BusinessLineEnum.getCode(info.getBusinessLine()));
            }
            if (info.getVehicleModelId() != null &&
                    vehicleModelMap.get(info.getVehicleModelId()) != null){
                vehicleInfo.setVehicleModelId(info.getVehicleModelId());
            }
            if (StringUtils.isNotBlank(info.getVehicleBodyColor()) &&
                    vehicleColorMap.get(info.getVehicleBodyColor()) != null){
                vehicleInfo.setVehicleColorId(vehicleColorMap.get(info.getVehicleBodyColor()));
            }
            if (StringUtils.isNotBlank(info.getEngineNo())){
                vehicleInfo.setEngineNo(info.getEngineNo());
            }
            if (StringUtils.isNotBlank(info.getEngineModel())){
                vehicleInfo.setEngineModel(info.getEngineModel());
            }
            vehicleInfo.setRetirementDateRegistrationCard(DateTimeUtils.stringToDate(info.getRetirementDateRegistrationCard(), DateTimeUtils.DATE_TYPE3));
            vehicleInfo.setRegistrationDateRegistrationCard(DateTimeUtils.stringToDate(info.getRegistrationDateRegistrationCard(), DateTimeUtils.DATE_TYPE3));
            vehicleInfo.setRealRetirementDate(DateTimeUtils.stringToDate(info.getRealRetirementDate(), DateTimeUtils.DATE_TYPE3));
            tableVehicleService.updateVehicle(vehicleInfo, tokenUserInfo);

            VehicleManagementLegacyInfo legacyInfo = legacyMap.get(info.getVin());
            if (StringUtils.isNotBlank(info.getOperationCertificateNumber())){
                legacyInfo.setOperatingNo(info.getOperationCertificateNumber());
            }
            if (StringUtils.isNotBlank(info.getIsOperating()) && operateTypeMap.get(info.getIsOperating()) != null) {
                legacyInfo.setOperateTypeId(operateTypeMap.get(info.getIsOperating()));
            }
            if (StringUtils.isNotBlank(info.getOperationCategory()) && operationCategoryMap.get(info.getOperationCategory()) != null) {
                legacyInfo.setOperationCategoryId(operationCategoryMap.get(info.getOperationCategory()));
            }
            if (StringUtils.isNotBlank(info.getLicensePlateType()) && vehicleCategoryMap.get(info.getLicensePlateType()) != null) {
                legacyInfo.setVehicleCategoryId(vehicleCategoryMap.get(info.getLicensePlateType()));
            }
            tableVehicleManagementLegacyService.updateSelectiveById(legacyInfo, tokenUserInfo);
            // 操作日志
            tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, "管理员批量修改信息", tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse importVehicleFill(ImportVehicleInfoRequest request, TokenUserInfo tokenUserInfo) {
        // 3-行驶证文件 4-产证文件 5-合格证文件 6-发票文件 7-购置税文件 8-营运证
        int fileType = request.getFileType();
        VehicleAttachmentTypeEnum vehicleAttachmentTypeEnum = VehicleAttachmentTypeEnum.getVehicleAttachmentType(fileType);
        if (vehicleAttachmentTypeEnum == null) {
            throw new ServiceException("上传文件枚举类型错误");
        }
        // 判断文件格式是否zip
        String filePath = request.getFilePath();
        if ((!StringUtils.endsWithIgnoreCase(filePath, ".zip"))) {
            throw new ServiceException("请上传正确的zip文件");
        }
        // 车架号命名文件
        List<String> fileNameList = new ArrayList<>();
        List<Map<String, Object>> fileList = new ArrayList<>();

        String mfsRootPath = Global.instance.mfsRootPath;
        ZipFile zipFile = CommonUtils.createZipFile(mfsRootPath + "/" + filePath);
        if (zipFile == null) {
            throw new ServiceException("zip文件压缩包解压失败");
        }
        String zipName = zipFile.getName();
        Enumeration<? extends ZipEntry> entries = zipFile.entries();
        while (entries.hasMoreElements()) {
            ZipEntry zipEntry = entries.nextElement();
            if (zipEntry.isDirectory()) {
                continue;
            }
            String zipEntryName = zipEntry.getName();
            // 判断上传文件类型文件格式
            String suffix = FilenameUtils.getExtension(zipEntryName);
            if (!suffix.equals("png") && !suffix.equals("jpg")
                    && !suffix.equals("pdf") && !suffix.equals("jpeg")) {
                throw new ServiceException("文件明细类型必须为图片或者pdf");
            }
            String fileName = zipEntryName.replace(zipName, "");
            Map<String, Object> fileMap = new HashMap<>();
            fileMap.put("suffix", suffix);
            fileMap.put("fileName", fileName);
            try {
                fileMap.put("inputStream", zipFile.getInputStream(zipEntry));
            } catch (IOException e) {
                log.error("zip文件上传失败", e);
                throw new ServiceException("zip文件上传失败");
            }
            if (zipEntryName.contains("/")) {
                int beginIndex = zipEntryName.indexOf("/") + 1;
                int endIndex = zipEntryName.lastIndexOf(".");
                fileName = zipEntryName.substring(beginIndex, endIndex);
                fileMap.put("fileName", zipEntryName.substring(beginIndex, zipEntryName.length()));
                fileMap.put("vin", fileName);
            }
            if (fileNameList.contains(fileName)) {
                throw new ServiceException("附件文件名称不可以重复");
            }
            fileNameList.add(fileName);
            fileList.add(fileMap);
        }
        // 根据文件名判断车辆信息是否存在-按照车架号查询
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(fileNameList);
        // 根据文件名判断车辆信息是否存在-按照车牌号查询
        List<VehicleInfo> vehiclePlateList = tableVehicleService.queryVehicleByLicensePlateList(fileNameList);
        List<VehicleInfo> vehicleList = new ArrayList<>();
        for (VehicleInfo vehicleInfo : vehicleInfoList) {
            vehicleList.add(vehicleInfo);
        }
        for (VehicleInfo vehicleInfo : vehiclePlateList) {
            vehicleList.add(vehicleInfo);
        }
        if (CollectionUtil.isEmpty(vehicleList)) {
            throw new ServiceException("上传文件车辆信息不存在");
        }
        Map<String, VehicleInfo> vinMap = vehicleList.stream().collect(Collectors.toMap(VehicleInfo::getVin, vehicleInfo -> vehicleInfo));
        Map<String, VehicleInfo> licensePlateMap = vehicleList.stream().collect(Collectors.toMap(VehicleInfo::getLicensePlate, vehicleInfo -> vehicleInfo));
        for (String vin : fileNameList) {
            VehicleInfo checkVin = vinMap.get(vin);
            VehicleInfo checkLicensePlate = licensePlateMap.get(vin);
            if (checkVin == null && checkLicensePlate == null) {
                throw new ServiceException(StrUtil.format("{}-车辆信息不存在", vin));
            }
        }

        // 文件上传路径
        String midPath = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDateTime.now()) + "/" + vehicleAttachmentTypeEnum.getFilePath();
        String fileDirPath = Global.instance.mfsRootPath + "/" + midPath;
        File fileDir = new File(fileDirPath);
        if (!fileDir.exists()) {
            fileDir.mkdirs();
        }
        // 进行文件上传 filetMap 遍历
        for (Map<String, Object> fileMap : fileList) {
            String fileName = (String) fileMap.get("fileName");
            String vin = (String) fileMap.get("vin");
            String suffix = (String) fileMap.get("suffix");
            InputStream inputStream = (InputStream) fileMap.get("inputStream");
            String newFileName = IdUtil.objectId() + "." + suffix;
            // 构建文件存储的完整路径
            Path uploadFilePath = Paths.get(fileDirPath, newFileName);
            // 将文件保存到指定路径
            try {
                Files.copy(inputStream, uploadFilePath);
            } catch (IOException e) {
                log.error("文件上传失败1", e);
                throw new ServiceException(StrUtil.format("{}-文件上传失败", vin));
            }

            String fileUrl = midPath + "/" + newFileName;
            VehicleInfo vehicleInfo = vinMap.get(vin);
            if (vehicleInfo == null) {
                vehicleInfo = licensePlateMap.get(vin);
            }
            if (fileType == VehicleAttachmentTypeEnum.vehicleLicense.getCode()) {
                vehicleInfo.setVehicleLicenseUrl(fileUrl);
            } else if (fileType == VehicleAttachmentTypeEnum.certificateOwnership.getCode()) {
                vehicleInfo.setCertificateOwnershipUrl(fileUrl);
            } else if (fileType == VehicleAttachmentTypeEnum.certificateConformity.getCode()) {
                vehicleInfo.setCertificateConformityUrl(fileUrl);
            } else if (fileType == VehicleAttachmentTypeEnum.vehicleInvoice.getCode()) {
                vehicleInfo.setVehicleInvoiceUrl(fileUrl);
            } else if (fileType == VehicleAttachmentTypeEnum.purchaseTax.getCode()) {
                vehicleInfo.setPurchaseTaxUrl(fileUrl);
            } else if (fileType == VehicleAttachmentTypeEnum.operatingPermit.getCode()) {
                vehicleInfo.setOperatingPermitUrl(fileUrl);
            }
            tableVehicleService.updateVehicle(vehicleInfo, tokenUserInfo);
            /*保存附件记录*/
            VehicleFileRecord vehicleFileRecord = new VehicleFileRecord();
            vehicleFileRecord.setFileTypeName(vehicleAttachmentTypeEnum.getDesc());
            vehicleFileRecord.setVin(vin);
            vehicleFileRecord.setFileName(fileName);
            vehicleFileRecord.setFileUrl(fileUrl);
            tableVehicleFileRecordService.insert(vehicleFileRecord, tokenUserInfo);
            // 操作记录
            tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, StrUtil.format("上传车辆{}附件信息", vehicleAttachmentTypeEnum.getDesc()), tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    public ResultResponse queryVehicleFileRecord(VinQueryRequest request) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehicleFileRecord> list = tableVehicleFileRecordService.queryVehicleFileRecord(request.getVin());
        PageInfo<VehicleFileRecord> pageInfo = new PageInfo<>(list);
        // 返回结果
        List<VehicleFileRecordResponse> responseList = new ArrayList<>();
        String mfsUrl = Global.instance.mfsUrl;
        list.forEach(record -> {
            VehicleFileRecordResponse response = BeanUtil.copyProperties(record, VehicleFileRecordResponse.class);
            response.setFileUrl(StrUtil.format("{}/{}", mfsUrl, record.getFileUrl()));
            responseList.add(response);
        });
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return ResultResponse.success(pageResponse);

    }

    @Override
    public ResultResponse queryVehicleDecorationRecord(VinQueryRequest request) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<String> vinList = Arrays.asList(request.getVin());
        List<VehicleDecoration> list = tableVehicleDecorationService.queryDecorationList(vinList);
        PageInfo<VehicleDecoration> pageInfo = new PageInfo<>(list);
        // 返回结果
        List<VehicleDecorationResponse> responseList = new ArrayList<>();
        list.forEach(userInfo -> {
            VehicleDecorationResponse response = BeanUtil.copyProperties(userInfo, VehicleDecorationResponse.class);
            responseList.add(response);
        });
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return ResultResponse.success(pageResponse);
    }

    @Override
    public ResultResponse queryVehicleDepreciationRecord(VinQueryRequest request) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehicleDepreciationData> list = tableVehicleDepreciationDataService.queryVehicleDepreciationDataList(request.getVin());
        PageInfo<VehicleDepreciationData> pageInfo = new PageInfo<>(list);
        List<VehicleDepreciationDataResponse> responseList = BeanUtil.copyToList(list, VehicleDepreciationDataResponse.class);
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return ResultResponse.success(pageResponse);
    }

    @Override
    public ResultResponse queryVehicleContractRecord(VinQueryRequest request) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehicleContract> list = tableVehicleContractService.queryVehicleContractByVin(request.getVin());
        PageInfo<VehicleContract> pageInfo = new PageInfo<>(list);
        List<VehicleContractDataResponse> responseList = BeanUtil.copyToList(list, VehicleContractDataResponse.class);
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return ResultResponse.success(pageResponse);
    }

    @Override
    public ResultResponse getComBoxVehicleInfo(String vin, String licensePlate) {
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleListByVin(vin, licensePlate);
        if (CollectionUtil.isNotEmpty(vehicleInfoList)) {
            List<VehicleBaseListResponse> responses = BeanUtil.copyToList(vehicleInfoList, VehicleBaseListResponse.class);
            return ResultResponse.success(responses);
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse saveVehicleDecoration(VehicleDecorationRequest request, TokenUserInfo tokenUserInfo) {
        VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(request.getVin());
        if (vehicleInfo == null) {
            return ResultResponse.businessFailed("车辆信息不存在");
        }
        VehicleManagementLegacyInfo vehicleManagementLegacyInfo = tableVehicleManagementLegacyService.queryVehicleByVin(request.getVin());
        if (vehicleManagementLegacyInfo == null) {
            return ResultResponse.businessFailed("车辆信息不存在");
        }
        // 保存装潢信息
        List<VehicleDecorationDto> vehicleDecorationList = request.getList();
        // 删除
        tableVehicleDecorationService.deleteByVin(request.getVin());
        for (VehicleDecorationDto decoration : vehicleDecorationList) {
            VehicleDecoration vehicleDecoration = BeanUtil.copyProperties(decoration, VehicleDecoration.class);
            vehicleDecoration.setVin(request.getVin());
            tableVehicleDecorationService.insert(vehicleDecoration, tokenUserInfo);
        }
        tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, "入库登记保存车辆装潢信息", tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse saveVehicleOtherInfo(VehicleOtherInfoRequest request, TokenUserInfo tokenUserInfo) {
        VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(request.getVin());
        if (vehicleInfo == null) {
            return ResultResponse.businessFailed("车辆主数据不存在");
        }
        VehicleManagementLegacyInfo vehicleManagementLegacyInfo = tableVehicleManagementLegacyService.queryVehicleByVin(request.getVin());
        if (vehicleManagementLegacyInfo == null) {
            return ResultResponse.businessFailed("车辆信息不存在");
        }
        vehicleManagementLegacyInfo.setOperationCategoryId(request.getOperationCategoryId());
        vehicleManagementLegacyInfo.setOperateTypeId(request.getOperateTypeId());
        vehicleManagementLegacyInfo.setOperatingNo(request.getOperatingNo());
        vehicleManagementLegacyInfo.setVehicleCategoryId(request.getVehicleCategoryId());

        vehicleInfo.setProductDate(DateTimeUtils.stringToDate(request.getProductDate(), DateTimeUtils.DATE_TYPE3));
        vehicleInfo.setIssuanceDate(DateTimeUtils.stringToDate(request.getIssuanceDate(), DateTimeUtils.DATE_TYPE3));
        vehicleInfo.setCertificateNumber(request.getCertificateNumber());
        vehicleInfo.setAssetCompanyId(request.getAssetCompanyId());
        vehicleInfo.setOwnOrganizationId(request.getOwnOrganizationId());
        vehicleInfo.setUsageOrganizationId(request.getUsageOrganizationId());
        vehicleInfo.setUsageAgeLimit(request.getUsageYears());
        vehicleInfo.setDepreciationAgeLimit(request.getDepreciationYears());
        vehicleInfo.setAreaId(request.getAreaId());
        BigDecimal totalPrice = new BigDecimal(0);
        String purchasePrice = request.getPurchasePrice();
        if (StringUtils.isNotBlank(purchasePrice)) {
            vehicleInfo.setPurchasePrice(new BigDecimal(purchasePrice));
            totalPrice = totalPrice.add(vehicleInfo.getPurchasePrice());
        }
        String purchaseTax = request.getPurchaseTax();
        if (StringUtils.isNotBlank(purchaseTax)) {
            vehicleInfo.setPurchaseTax(new BigDecimal(purchaseTax));
            totalPrice = totalPrice.add(vehicleInfo.getPurchaseTax());
        }

        String licensePlatePrice = request.getLicensePlatePrice();
        if (StringUtils.isNotBlank(licensePlatePrice)) {
            vehicleInfo.setLicensePlatePrice(new BigDecimal(licensePlatePrice));
            totalPrice = totalPrice.add(vehicleInfo.getLicensePlatePrice());
        }
        String licensePlateOtherPrice = request.getLicensePlateOtherPrice();
        if (StringUtils.isNotBlank(licensePlateOtherPrice)) {
            vehicleInfo.setLicensePlateOtherPrice(new BigDecimal(licensePlateOtherPrice));
            totalPrice = totalPrice.add(vehicleInfo.getLicensePlateOtherPrice());
        }
        String upholsterPrice = request.getUpholsterPrice();
        if (StringUtils.isNotBlank(upholsterPrice)) {
            vehicleInfo.setUpholsterPrice(new BigDecimal(upholsterPrice));
            totalPrice = totalPrice.add(vehicleInfo.getUpholsterPrice());
        }
        vehicleInfo.setTotalPrice(totalPrice);
        tableVehicleService.updateVehicle(vehicleInfo, tokenUserInfo);
        tableVehicleManagementLegacyService.updateSelectiveById(vehicleManagementLegacyInfo, tokenUserInfo);
        // 保存车辆操作日志
        tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, "入库登记保存车辆其他信息", tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse saveVehicleAttachment(VehicleAttachmentRequest request, TokenUserInfo tokenUserInfo) {
        String vin = request.getVin();
        VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vin);
        if (vehicleInfo == null) {
            return ResultResponse.businessFailed("车辆信息不存在");
        }
        String mfsUrl = Global.instance.mfsUrl;
        // 保存附件列表
        List<VehicleAttachmentDto> list = request.getList();
        // 去重判断
        List<Integer> distinctList = list.stream().map(VehicleAttachmentDto::getFileType).distinct().collect(Collectors.toList());
        if (distinctList.size() != distinctList.size()) {
            return ResultResponse.businessFailed("文件类型不能重复");
        }
        vehicleInfo.setVehicleLicenseUrl(StringUtils.EMPTY);
        vehicleInfo.setCertificateOwnershipUrl(StringUtils.EMPTY);
        vehicleInfo.setCertificateConformityUrl(StringUtils.EMPTY);
        vehicleInfo.setVehicleInvoiceUrl(StringUtils.EMPTY);
        vehicleInfo.setPurchaseTaxUrl(StringUtils.EMPTY);
        vehicleInfo.setOperatingPermitUrl(StringUtils.EMPTY);
        for (VehicleAttachmentDto attachmentDto : list) {
            String filePath = attachmentDto.getFilePath();
            if (filePath.contains(mfsUrl)) {
                attachmentDto.setFilePath(filePath.replace(mfsUrl + "/", ""));
            }
            //文件类型 3-行驶证文件 4-产证文件 5-合格证文件 6-发票文件 7-购置税文件 8-营运证
            int fileType = attachmentDto.getFileType();
            VehicleAttachmentTypeEnum vehicleAttachmentTypeEnum = VehicleAttachmentTypeEnum.getVehicleAttachmentType(fileType);
            if (fileType == VehicleAttachmentTypeEnum.vehicleLicense.getCode()) {
                vehicleInfo.setVehicleLicenseUrl(attachmentDto.getFilePath());
            } else if (fileType == VehicleAttachmentTypeEnum.certificateOwnership.getCode()) {
                vehicleInfo.setCertificateOwnershipUrl(attachmentDto.getFilePath());
            } else if (fileType == VehicleAttachmentTypeEnum.certificateConformity.getCode()) {
                vehicleInfo.setCertificateConformityUrl(attachmentDto.getFilePath());
            } else if (fileType == VehicleAttachmentTypeEnum.vehicleInvoice.getCode()) {
                vehicleInfo.setVehicleInvoiceUrl(attachmentDto.getFilePath());
            } else if (fileType == VehicleAttachmentTypeEnum.purchaseTax.getCode()) {
                vehicleInfo.setPurchaseTaxUrl(attachmentDto.getFilePath());
            } else if (fileType == VehicleAttachmentTypeEnum.operatingPermit.getCode()) {
                vehicleInfo.setOperatingPermitUrl(attachmentDto.getFilePath());
            }
            tableVehicleService.updateVehicle(vehicleInfo, tokenUserInfo);
            // 保存上传附件记录
            if (!filePath.contains(mfsUrl)) {
                VehicleFileRecord vehicleFileRecord = new VehicleFileRecord();
                vehicleFileRecord.setVin(vin);
                vehicleFileRecord.setFileTypeName(vehicleAttachmentTypeEnum.getDesc());
                vehicleFileRecord.setFileName(attachmentDto.getFileName());
                vehicleFileRecord.setFileUrl(attachmentDto.getFilePath());
                tableVehicleFileRecordService.insert(vehicleFileRecord, tokenUserInfo);
                // 保存车辆操作日志
                tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, StrUtil.format("入库保存车辆{}附件信息-", vehicleAttachmentTypeEnum.getDesc()), tokenUserInfo);
            }
        }
        return ResultResponse.success();
    }

    @Override
    public ResultResponse batchSaveVehicleAttachment(BatchSaveVehicleAttachmentRequest request, TokenUserInfo tokenUserInfo) {
        //文件类型 3-行驶证文件 4-产证文件 5-合格证文件 6-发票文件 7-购置税文件 8-营运证
        int fileType = request.getFileType();
        VehicleAttachmentTypeEnum vehicleAttachmentTypeEnum = VehicleAttachmentTypeEnum.getVehicleAttachmentType(fileType);
        if (vehicleAttachmentTypeEnum == null) {
            return ResultResponse.businessFailed("上传文件枚举类型错误");
        }
        List<VehicleAttachmentDto> list = request.getList();
        // 去重判断
        List<String> distinctList = list.stream().map(VehicleAttachmentDto::getFileName).distinct().collect(Collectors.toList());
        if (distinctList.size() != distinctList.size()) {
            return ResultResponse.businessFailed("文件名称不能重复");
        }
        for (VehicleAttachmentDto attachmentDto : list) {
            String filePath = attachmentDto.getFilePath();
            String fileName = attachmentDto.getFileName();
            String vin = fileName.substring(0, fileName.lastIndexOf("."));
            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vin);
            if (vehicleInfo == null) {
                vehicleInfo = tableVehicleService.queryVehicleByLicensePlate(vin);
                if (vehicleInfo == null) {
                    throw new ServiceException(StrUtil.format("文件【{}】未查询到查了信息", fileName));
                }
            }
            if (fileType == VehicleAttachmentTypeEnum.vehicleLicense.getCode()) {
                vehicleInfo.setVehicleLicenseUrl(filePath);
            } else if (fileType == VehicleAttachmentTypeEnum.certificateOwnership.getCode()) {
                vehicleInfo.setCertificateOwnershipUrl(filePath);
            } else if (fileType == VehicleAttachmentTypeEnum.certificateConformity.getCode()) {
                vehicleInfo.setCertificateConformityUrl(filePath);
            } else if (fileType == VehicleAttachmentTypeEnum.vehicleInvoice.getCode()) {
                vehicleInfo.setVehicleInvoiceUrl(filePath);
            } else if (fileType == VehicleAttachmentTypeEnum.purchaseTax.getCode()) {
                vehicleInfo.setPurchaseTaxUrl(filePath);
            } else if (fileType == VehicleAttachmentTypeEnum.operatingPermit.getCode()) {
                vehicleInfo.setOperatingPermitUrl(filePath);
            }
            tableVehicleService.updateVehicle(vehicleInfo, tokenUserInfo);

            VehicleFileRecord vehicleFileRecord = new VehicleFileRecord();
            vehicleFileRecord.setVin(vin);
            vehicleFileRecord.setFileTypeName(vehicleAttachmentTypeEnum.getDesc());
            vehicleFileRecord.setFileName(attachmentDto.getFileName());
            vehicleFileRecord.setFileUrl(attachmentDto.getFilePath());
            tableVehicleFileRecordService.insert(vehicleFileRecord, tokenUserInfo);
            // 保存车辆操作日志
            tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, StrUtil.format("入库保存车辆{}附件信息-", vehicleAttachmentTypeEnum.getDesc()), tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    public void exportAssetVehicleInfo(SearchAssetVehicleRequest request, TokenUserInfo tokenUserInfo) {
        //导出文件类型
        ExportFileTypeEnum fileTypeEnum = ExportFileTypeEnum.EXPORT_ASSET_VEHICLE_INFO;
        //导出模板信息
        InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream("template/导出车辆数据.xlsx");

        //查询字典表相关信息
        List<String> systemCodeList = Arrays.asList(
                //商品车型
                DataDictEnum.VEHICLE_ABBREVIATION.getValue(),
                //车辆类型
                DataDictEnum.VEHICLE_TYPE.getValue(),
                //使用性质
                DataDictEnum.USAGE.getValue(),
                //营运/非营运标识
                DataDictEnum.OPERATE_TYPE.getValue(),
                //能源类型
                DataDictEnum.GAS_TYPE.getValue(),
                //排放标准
                DataDictEnum.EXHAUST.getValue(),
                //车辆拥有公司
                DataDictEnum.OWNER.getValue()
        );
        Map<String, Map<Integer, String>> dataMaintainDictMap = dataDictService.getDataMaintainDictMap(systemCodeList);


        //查询机构映射信息
        Map<Long, String> orgNameMap = orgService.getOrgNameMap();

        //开始导出文件
        fileService.export(fileTypeEnum, templateStream, tokenUserInfo,
                ((excelWriter, writeSheet) -> {
                    //是否查询到导出数据
                    boolean hasResult = false;
                    //标识位
                    Long index = -1L;

                    while (true) {
                        //分批查询填充导出数据
                        List<ExportAssetVehicleInfoDto> exportDataList = tableVehicleService.exportAssetVehicleInfo(index, request, tokenUserInfo);
                        if (exportDataList.isEmpty()) {
                            break;
                        }
                        //存在导出数据
                        hasResult = true;
                        //遍历处理查询字段
                        for (ExportAssetVehicleInfoDto exportAssetVehicleInfoDto : exportDataList) {
                            transExportVehicleInfo(exportAssetVehicleInfoDto, orgNameMap, dataMaintainDictMap);
                        }
                        //更新标识位
                        index = exportDataList.get(exportDataList.size() - 1).getId();
                        excelWriter.fill(exportDataList, writeSheet);
                    }
                    return hasResult;
                }
                ));
    }

    @Override
    public SearchAssetVehicleSyncDataResponse searchAssetVehicleSyncData(SearchAssetVehicleSyncDataRequest request) {
        //查询字典表相关信息
        List<String> systemCodeList = Arrays.asList(
                //商品车型
                DataDictEnum.VEHICLE_ABBREVIATION.getValue(),
                //使用性质
                DataDictEnum.USAGE.getValue(),
                //能源类型
                DataDictEnum.GAS_TYPE.getValue(),
                //车身颜色
                DataDictEnum.VEHICLE_COLOR.getValue(),
                //制造商
                DataDictEnum.MANUFACTURER.getValue(),
                //车辆类型
                DataDictEnum.VEHICLE_TYPE.getValue()
        );
        Map<String, Map<Integer, String>> dataMaintainDictMap = dataDictService.getDataMaintainDictMap(systemCodeList);
        //查询机构映射信息
        Map<Long, String> orgNameMap = orgService.getOrgNameMap();
        //资产车辆拥有公司映射信息
        Map<Integer, DataOwnerInfo> ownerInfoMap = dataDictService.getOwnerMap();
        List<SyncAssetVehicleInfoDto> syncAssetVehicleInfoList = tableVehicleService.searchAssetVehicleSyncInfo(request);
        for (SyncAssetVehicleInfoDto syncAssetVehicleInfoDto : syncAssetVehicleInfoList) {
            transVehicleSyncInfo(syncAssetVehicleInfoDto, orgNameMap, ownerInfoMap, dataMaintainDictMap);
        }

        //记录标识位，用于下一次基于该标识继续查询
        Long lastIndex = null;
        if (!syncAssetVehicleInfoList.isEmpty()) {
            lastIndex = syncAssetVehicleInfoList.get(syncAssetVehicleInfoList.size() - 1).getId();
        }
        return new SearchAssetVehicleSyncDataResponse(syncAssetVehicleInfoList, lastIndex);
    }

    @Override
    public SearchDatabaseTableSyncDataResponse<SyncDatabaseVehicleDto> searchVehicleDatabaseSyncData(SearchDatabaseTableSyncDataRequest request) {
        List<SyncDatabaseVehicleDto> list = tableVehicleService.searchVehicleDatabaseSyncInfo(request);

        //记录标识位，用于下一次基于该标识继续查询
        Long lastIndex = null;
        if (!list.isEmpty()) {
            lastIndex = list.get(list.size() - 1).getId();
        }
        return new SearchDatabaseTableSyncDataResponse<>("Vehicle", list, lastIndex);
    }

    @Override
    public SearchDatabaseTableSyncDataResponse<SyncDatabaseVehicleAssetDto> searchVehicleAssetDatabaseSyncData(SearchDatabaseTableSyncDataRequest request) {
        List<SyncDatabaseVehicleAssetDto> list = tableVehicleService.searchVehicleAssetDatabaseSyncInfo(request);

        //记录标识位，用于下一次基于该标识继续查询
        Long lastIndex = null;
        if (!list.isEmpty()) {
            lastIndex = list.get(list.size() - 1).getId();
        }

        return new SearchDatabaseTableSyncDataResponse<>("VehicleAsset", list, lastIndex);
    }

    @Override
    public GetCarInfoResponse.CarInfo getCarInfo(String vin) {
        //调用HTTP接口查询
        String url = Global.instance.getCarInfoUrl;
        Map<String, Object> params = new HashMap<>();
        params.put("frameNo", vin);
        ResponseEntity<GetCarInfoResponse> response = restTemplate.postForEntity(url, params, GetCarInfoResponse.class);

        //组装返回结果
        if (Objects.requireNonNull(response.getBody()).getPayload() != null &&
                Objects.requireNonNull(response.getBody()).getPayload().size() > 0) {
            return response.getBody().getPayload().get(0);
        }
        return null;
    }

    @Override
    public List<GetCarInfoResponse.CarInfo> getCarInfoList(Long companyId) {
        //调用HTTP接口查询
        String url = Global.instance.getCarInfoUrl;
        Map<String, Object> params = new HashMap<>();
        params.put("companyId", companyId);
        ResponseEntity<GetCarInfoResponse> response = restTemplate.postForEntity(url, params, GetCarInfoResponse.class);

        //组装返回结果
        if (Objects.requireNonNull(response.getBody()).getPayload() != null &&
                Objects.requireNonNull(response.getBody()).getPayload().size() > 0) {
            return response.getBody().getPayload();
        }
        return Collections.emptyList();
    }

    @Override
    public ResultResponse<Void> syncAllVehicleInfoFromDaZhong() {
        log.info("开始从大众交通核心系统同步车辆信息");
        long startTime = System.currentTimeMillis();

        try {
            // 查询t_vehicle_info表全量数据
            List<VehicleInfo> allVehicles = tableVehicleService.selectAllUsageOrganizationId();

            if (CollectionUtil.isEmpty(allVehicles)) {
                log.warn("没有找到需要同步的车辆信息");
                return ResultResponse.success("没有找到需要同步的车辆信息");
            }

            // 按公司ID分组车辆信息，便于批量处理
            Map<Long, List<VehicleInfo>> vehicleByCompany = allVehicles.stream()
                .filter(v -> v.getUsageOrganizationId() != null)
                .collect(Collectors.groupingBy(VehicleInfo::getUsageOrganizationId));

            log.info("共查询到{}个公司需要拉取同步", vehicleByCompany.size());

            // 使用原子类计数器来跟踪处理结果，保证线程安全
            final AtomicInteger successCount = new AtomicInteger(0);
            final AtomicInteger failCount = new AtomicInteger(0);
            final AtomicInteger totalProcessed = new AtomicInteger(0);

            // 使用CompletableFuture进行并行处理
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (Map.Entry<Long, List<VehicleInfo>> entry: vehicleByCompany.entrySet()) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    Long companyId = entry.getKey();
                    List<VehicleInfo> companyVehicles = entry.getValue();
                    int companyVehicleProcessCount = 0;
                    try {
                        // 调用getCarInfo获取核心系统车辆信息
                        List<GetCarInfoResponse.CarInfo> carInfoList = getCarInfoList(companyId);
                        if (CollectionUtil.isEmpty(carInfoList)) {
                            log.warn("公司id:{}在核心系统中未找到对应信息，跳过同步", companyId);
                            return;
                        }

                        // 创建车架号到核心系统车辆信息的映射，便于快速查找
                        Map<String, GetCarInfoResponse.CarInfo> carInfoMap = carInfoList.stream()
                                .filter(car -> StringUtils.isNotBlank(car.getFrameNo()))
                                .collect(Collectors.toMap(
                                        GetCarInfoResponse.CarInfo::getFrameNo,
                                        car -> car,
                                        (existing, replacement) -> existing // 如有重复，保留第一个
                                ));

                        // 按运营状态和所属车队分组，以便批量更新
                        Map<Integer, List<String>> operatingStatusMap = new HashMap<>();
                        Map<String, List<String>> belongingTeamMap = new HashMap<>();

                        for (VehicleInfo vehicleInfo : companyVehicles) {
                            if (StringUtils.isBlank(vehicleInfo.getVin())) {
                                continue;
                            }

                            GetCarInfoResponse.CarInfo carInfo = carInfoMap.get(vehicleInfo.getVin());
                            if (carInfo == null) {
                                continue;
                            }

                            // 按运营状态分组
                            if (carInfo.getCarStatusId() != null && carInfo.getCarStatusId() != 0) {
                                operatingStatusMap.computeIfAbsent(carInfo.getCarStatusId(), k -> new ArrayList<>())
                                    .add(vehicleInfo.getVin());
                            }

                            // 按所属车队分组
                            if (StringUtils.isNotBlank(carInfo.getCarTeamName())) {
                                belongingTeamMap.computeIfAbsent(carInfo.getCarTeamName(), k -> new ArrayList<>())
                                    .add(vehicleInfo.getVin());
                            }
                            companyVehicleProcessCount ++;
                            totalProcessed.addAndGet(1);
                        }

                        // 批量更新运营状态
                        for (Map.Entry<Integer, List<String>> statusEntry : operatingStatusMap.entrySet()) {
                            Integer operatingStatus = statusEntry.getKey();
                            List<String> vinList = statusEntry.getValue();
                            int updatedCount = tableVehicleService.batchUpdateVehicleOperatingStatus(vinList, operatingStatus);
                            successCount.addAndGet(updatedCount);
                            log.info("批量更新运营状态 {} 的车辆 {} 辆", operatingStatus, vinList.size());
                        }

                        // 批量更新所属车队
                        for (Map.Entry<String, List<String>> teamEntry : belongingTeamMap.entrySet()) {
                            String belongingTeam = teamEntry.getKey();
                            List<String> vinList = teamEntry.getValue();
                            int updatedCount = tableVehicleService.batchUpdateVehicleBelongingTeam(vinList, belongingTeam);
                            successCount.addAndGet(updatedCount);
                            log.info("批量更新所属车队 {} 的车辆 {} 辆", belongingTeam, vinList.size());
                        }
                        log.info("公司id:{}同步完成，处理{}条记录", companyId, companyVehicleProcessCount);
                    } catch (Exception e) {
                        log.error("同步公司id:{}的车辆信息时发生异常", companyId, e);
                        failCount.addAndGet(vehicleByCompany.size());
                    }
                });

                futures.add(future);
            }

            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            long endTime = System.currentTimeMillis();
            String resultMsg = String.format("车辆信息同步完成，成功:%d条，失败:%d条，总耗时:%d毫秒",
                successCount.get(), failCount.get(), (endTime - startTime));
            log.info(resultMsg);

            return ResultResponse.success(resultMsg);
        } catch (Exception e) {
            log.error("同步车辆信息过程中发生异常", e);
            return ResultResponse.businessFailed("同步车辆信息失败：" + e.getMessage());
        }
    }

    @Override
    public ResultResponse<Void> syncVehicleToSaasManually(boolean isFullSync) {
        log.info("手动触发同步车辆信息到SAAS系统，同步模式：{}", isFullSync ? "全量同步" : "增量同步");

        List<OrgInfo> orgInfoList = tableOrgInfoService.queryAllOrgInfo();

        try {
            Long lastId = 0L;
            int batchCount = 0;
            boolean hasMoreData = true;

            // 创建线程池
            ThreadPoolExecutor executor = new ThreadPoolExecutor(
                    5, 10, 60, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(100),
                    r -> {
                        Thread t = new Thread(r);
                        t.setName("saas-sync-pool-" + t.getId());
                        return t;
                    },
                    new ThreadPoolExecutor.CallerRunsPolicy());

            // 分批处理数据
            while (hasMoreData) {
                // 获取当前批次数据
                final List<Map<String, Object>> batchData = getVehicleSyncDataForSaas(lastId, orgInfoList, isFullSync);

                if (CollectionUtil.isEmpty(batchData)) {
                    hasMoreData = false;
                    continue;
                }

                batchCount++;
                // 记录最后一条数据的ID，用于下次查询
                final Long currentBatchLastId = Long.valueOf(batchData.get(batchData.size() - 1).get("id").toString());
                lastId = currentBatchLastId;

                // 异步处理当前批次
                executor.execute(() -> {
                    try {
                        syncBatchToSaas(batchData);
                    } catch (Exception e) {
                        log.error("批次数据同步请求发送异常，lastId={}", currentBatchLastId, e);
                    }
                });
            }

            // 关闭线程池（不再接收新任务，但会处理完已提交的任务）
            executor.shutdown();

            log.info("车辆信息同步到SAAS系统任务已提交，共处理{}个批次", batchCount);
            return ResultResponse.success("同步任务已提交，共处理" + batchCount + "个批次");
        } catch (Exception e) {
            log.error("手动同步车辆信息到SAAS系统过程中发生异常", e);
            return ResultResponse.businessFailed("同步车辆信息失败：" + e.getMessage());
        }
    }

    /**
     * 同步一批数据到SAAS系统
     * @param batch 批次数据
     * @return 是否同步成功
     */
    private boolean syncBatchToSaas(List<Map<String, Object>> batch) {
        try {
            // 设置HTTP请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("syncKey", Global.instance.saasSyncKey);
            params.put("batchData", batch);
            
            // 创建自定义的RestTemplate实例，设置更长的超时时间
            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setConnectTimeout(30000); // 连接超时时间30秒
            factory.setReadTimeout(120000);   // 读取超时时间120秒
            RestTemplate customRestTemplate = new RestTemplate(factory);
            
            // 发送HTTP请求
            HttpEntity<String> httpEntity = new HttpEntity<>(JSONUtil.toJsonStr(params), headers);
            log.info("同步车辆数据到SAAS系统，请求URL: {}", Global.instance.saasVehicleSyncUrl);
            log.info("同步车辆数据到SAAS系统，请求参数: {}", JSONUtil.toJsonStr(params));
            ResponseEntity<JSONObject> responseEntity = customRestTemplate.postForEntity(
                    Global.instance.saasVehicleSyncUrl, httpEntity, JSONObject.class);
            log.info("同步车辆数据到SAAS系统，响应结果: {}", JSONUtil.toJsonStr(responseEntity.getBody()));
            
            // 处理响应结果
            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                JSONObject responseBody = responseEntity.getBody();
                if (responseBody != null && responseBody.getInteger("code") == 200) {
                    log.info("批次同步成功，共 {} 条数据", batch.size());
                    return true;
                } else {
                    log.error("批次同步失败，SAAS系统返回错误: {}", responseBody.getString("message"));
                    return false;
                }
            } else {
                log.error("批次同步失败，HTTP请求失败，状态码: {}", responseEntity.getStatusCodeValue());
                return false;
            }
        } catch (Exception e) {
            log.error("批次同步过程中发生异常", e);
            return false;
        }
    }

    private List<Map<String, Object>> getVehicleSyncDataForSaas(Long lastId, List<OrgInfo> orgInfoList, boolean isFullSync) {
        log.info("获取需要同步到SAAS系统的车辆数据，lastId={}，同步模式：{}", lastId, isFullSync ? "全量同步" : "增量同步");
        
        // 查询当前批次数据
        List<VehicleInfo> batchVehicleList = tableVehicleService.selectSyncSaasVehicleInfo(lastId, 1000, isFullSync);
        
        if (CollectionUtil.isEmpty(batchVehicleList)) {
            return Collections.emptyList();
        }
        
        // 转换当前批次数据并返回
        return batchVehicleList.stream()
                .map(vehicleInfo -> convertToSaasFormat(vehicleInfo, orgInfoList))
                .collect(Collectors.toList());
    }

    /**
     * 将车辆信息转换为SAAS系统需要的格式
     */
    private Map<String, Object> convertToSaasFormat(VehicleInfo vehicleInfo, List<OrgInfo> orgInfoList) {
        Map<String, Object> map = new HashMap<>();
        // 基本信息
        map.put("id", vehicleInfo.getId());
        map.put("vin", vehicleInfo.getVin());
        map.put("vehicleNo", vehicleInfo.getLicensePlate());
        map.put("vehicleModelId", vehicleInfo.getVehicleModelId());
        // 获取车辆所属机构编码
        String vehicleOrgCode = orgInfoList.stream()
                .filter(orgInfo -> orgInfo.getId().equals(vehicleInfo.getOwnOrganizationId()))
                .map(OrgInfo::getCompanyCode)
                .findFirst()
                .orElse(null);
        map.put("vehicleOrgId", vehicleOrgCode);
        // 获取运营机构编码
        String operationOrgCode = orgInfoList.stream()
                .filter(orgInfo -> orgInfo.getId().equals(vehicleInfo.getOwnOrganizationId()))
                .map(OrgInfo::getCompanyCode)
                .findFirst()
                .orElse(null);
        map.put("operationOrgId", operationOrgCode);
        map.put("productLine", vehicleInfo.getProductLine());
        map.put("subProductLine", vehicleInfo.getBusinessLine());
        map.put("engineId", vehicleInfo.getEngineNo());
        map.put("registerDate", DateTimeUtils.dateToString(vehicleInfo.getRegistrationDateRegistrationCard(), DateTimeUtils.DATE_TYPE1));
        map.put("totalMileage", vehicleInfo.getLatestTotalMileage());
        map.put("propertyStatus", vehicleInfo.getPropertyStatus());
        return map;
    }

    /**
     * 处理车辆同步信息
     *
     * @param syncAssetVehicleData 原始数据
     * @param orgNameMap           机构名称映射表
     * @param dataMaintainDictMap  字典映射表
     */
    private void transVehicleSyncInfo(SyncAssetVehicleInfoDto syncAssetVehicleData, Map<Long, String> orgNameMap,
                                      Map<Integer, DataOwnerInfo> ownerInfoMap, Map<String, Map<Integer, String>> dataMaintainDictMap) {
        //使用机构
        syncAssetVehicleData.setCompany(orgNameMap.get(syncAssetVehicleData.getCompanyId()));

        //车辆拥有公司
        DataOwnerInfo ownerInfo = ownerInfoMap.get(syncAssetVehicleData.getOwnerId());
        if (ownerInfo != null) {
            syncAssetVehicleData.setOwner(ownerInfo.getName());
            syncAssetVehicleData.setAddress(ownerInfo.getAddress());
        }

        //使用性质
        syncAssetVehicleData.setLicenseType(getDictValueName(DataDictEnum.USAGE, syncAssetVehicleData.getLicenseTypeId(), dataMaintainDictMap));
        //商品车型名称
        syncAssetVehicleData.setName(getDictValueName(DataDictEnum.VEHICLE_ABBREVIATION, syncAssetVehicleData.getCarNameId(), dataMaintainDictMap));
        //车身颜色
        syncAssetVehicleData.setCarColor(getDictValueName(DataDictEnum.VEHICLE_COLOR, syncAssetVehicleData.getCarColorId(), dataMaintainDictMap));
        //制造商
        syncAssetVehicleData.setProduct(getDictValueName(DataDictEnum.MANUFACTURER, syncAssetVehicleData.getProductId(), dataMaintainDictMap));
        //能源类型
        syncAssetVehicleData.setOilTrade(getDictValueName(DataDictEnum.GAS_TYPE, syncAssetVehicleData.getOilTradeId(), dataMaintainDictMap));
        //车辆类型
        syncAssetVehicleData.setCarClass(getDictValueName(DataDictEnum.VEHICLE_TYPE, syncAssetVehicleData.getCarClassId(), dataMaintainDictMap));
    }

    /**
     * 处理车辆导出字段
     *
     * @param exportVehicleData   原始数据
     * @param orgNameMap          机构名称映射表
     * @param dataMaintainDictMap 字典映射表
     */
    public void transExportVehicleInfo(ExportAssetVehicleInfoDto exportVehicleData, Map<Long, String> orgNameMap,
                                       Map<String, Map<Integer, String>> dataMaintainDictMap) {
        //额度类型
        exportVehicleData.setQuotaTypeStr(QuotaTypeEnum.getDesc(exportVehicleData.getQuotaType()));
        //资产状态
        exportVehicleData.setPropertyStatusStr(PropertyStatusEnum.getDesc(exportVehicleData.getPropertyStatus()));
        //产品线
        exportVehicleData.setProductLineStr(ProductLineEnum.getDesc(exportVehicleData.getProductLine()));
        //业务线
        exportVehicleData.setBusinessLineStr(BusinessLineEnum.getDesc(exportVehicleData.getBusinessLine()));
        //运营状态
        exportVehicleData.setOperatingStatusStr(OperatingStatusEnum.getDesc(exportVehicleData.getOperatingStatus()));

        //额度资产归属公司
        exportVehicleData.setQuotaAssetCompanyName(getDictValueName(DataDictEnum.OWNER, exportVehicleData.getQuotaAssetCompanyId(), dataMaintainDictMap));
        //资产所属公司
        exportVehicleData.setAssetCompanyName(getDictValueName(DataDictEnum.OWNER, exportVehicleData.getAssetCompanyId(), dataMaintainDictMap));
        //实际运营公司（所属）
        exportVehicleData.setOwnOrganizationName(orgNameMap.get(exportVehicleData.getOwnOrganizationId()));
        //实际运营公司（使用）
        exportVehicleData.setUsageOrganizationName(orgNameMap.get(exportVehicleData.getUsageOrganizationId()));

        //强制报废日期
        exportVehicleData.setRetirementDateStrRegistrationCard(DateTimeUtils.dateToString(exportVehicleData.getRetirementDateRegistrationCard(), DateTimeUtils.DATE_TYPE3));
        //投产日期
        exportVehicleData.setStartDateStr(DateTimeUtils.dateToString(exportVehicleData.getStartDate(), DateTimeUtils.DATE_TYPE3));
        //上牌日期
        exportVehicleData.setLicenseDateStr(DateTimeUtils.dateToString(exportVehicleData.getLicenseDate(), DateTimeUtils.DATE_TYPE3));
        //折旧开始日期
        exportVehicleData.setDepreciationStartDateStr(DateTimeUtils.dateToString(exportVehicleData.getDepreciationStartDate(), DateTimeUtils.DATE_TYPE3));
        //折旧开始日期
        exportVehicleData.setRealRetirementDateStr(DateTimeUtils.dateToString(exportVehicleData.getRealRetirementDate(), DateTimeUtils.DATE_TYPE3));

        //车辆类型
        exportVehicleData.setVehicleTypeStrRegistrationCard(getDictValueName(DataDictEnum.VEHICLE_TYPE, exportVehicleData.getVehicleTypeRegistrationCard(), dataMaintainDictMap));
        //使用性质
        exportVehicleData.setUsageRegistrationCard(getDictValueName(DataDictEnum.USAGE, exportVehicleData.getUsageIdRegistrationCard(), dataMaintainDictMap));
        //营运/非营运标识
        exportVehicleData.setOperateTypeStr(getDictValueName(DataDictEnum.OPERATE_TYPE, exportVehicleData.getOperateTypeId(), dataMaintainDictMap));
        //商品车型名称
        exportVehicleData.setVehicleAbbreviationName(getDictValueName(DataDictEnum.VEHICLE_ABBREVIATION, exportVehicleData.getVehicleAbbreviationId(), dataMaintainDictMap));
        //能源类型
        exportVehicleData.setGasTypeStr(getDictValueName(DataDictEnum.GAS_TYPE, exportVehicleData.getGasTypeId(), dataMaintainDictMap));
        //排放标准
        exportVehicleData.setExhaustStr(getDictValueName(DataDictEnum.EXHAUST, exportVehicleData.getExhaustId(), dataMaintainDictMap));
    }


    /**
     * 获取字典表对应的中文名称
     *
     * @param dataDictEnum        字典枚举类
     * @param value               code值
     * @param dataMaintainDictMap 字典表集合（需提前查询）
     * @return name 中文名称
     */
    private String getDictValueName(DataDictEnum dataDictEnum, Integer value, Map<String, Map<Integer, String>> dataMaintainDictMap) {
        if (dataDictEnum == null || dataMaintainDictMap == null || value == null) {
            return StringUtils.EMPTY;
        }

        try {
            Map<Integer, String> dictMap = dataMaintainDictMap.get(dataDictEnum.getValue());
            if (dictMap == null) {
                return StringUtils.EMPTY;
            }

            return dictMap.getOrDefault(value, StringUtils.EMPTY);
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }

    @Override
    public ResultResponse<Void> syncVehicleModelToSaasManually(boolean isFullSync) {
        log.info("手动触发同步车型信息到SAAS系统，同步模式：{}", isFullSync ? "全量同步" : "增量同步");

        try {
            Long lastId = 0L;
            int batchCount = 0;
            boolean hasMoreData = true;

            // 创建线程池
            ThreadPoolExecutor executor = new ThreadPoolExecutor(
                    5, 10, 60, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(100),
                    r -> {
                        Thread t = new Thread(r);
                        t.setName("saas-model-sync-pool-" + t.getId());
                        return t;
                    },
                    new ThreadPoolExecutor.CallerRunsPolicy());

            // 分批处理数据
            while (hasMoreData) {
                // 获取当前批次数据
                final List<Map<String, Object>> batchData = getVehicleModelSyncDataForSaas(lastId, isFullSync);

                if (CollectionUtil.isEmpty(batchData)) {
                    hasMoreData = false;
                    continue;
                }

                batchCount++;
                // 记录最后一条数据的ID，用于下次查询
                final Long currentBatchLastId = Long.valueOf(batchData.get(batchData.size() - 1).get("id").toString());
                lastId = currentBatchLastId;

                // 异步处理当前批次
                executor.execute(() -> {
                    try {
                        syncModelBatchToSaas(batchData);
                    } catch (Exception e) {
                        log.error("车型批次数据同步请求发送异常，lastId={}", currentBatchLastId, e);
                    }
                });
            }

            // 关闭线程池（不再接收新任务，但会处理完已提交的任务）
            executor.shutdown();

            log.info("车型信息同步到SAAS系统任务已提交，共处理{}个批次", batchCount);
            return ResultResponse.success("同步任务已提交，共处理" + batchCount + "个批次");
        } catch (Exception e) {
            log.error("手动同步车型信息到SAAS系统过程中发生异常", e);
            return ResultResponse.businessFailed("同步车型信息失败：" + e.getMessage());
        }
    }

    /**
     * 同步一批车型数据到SAAS系统
     * @param batch 批次数据
     * @return 是否同步成功
     */
    private boolean syncModelBatchToSaas(List<Map<String, Object>> batch) {
        try {
            // 设置HTTP请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("syncKey", Global.instance.saasSyncKey);
            params.put("batchData", batch);
            
            // 创建自定义的RestTemplate实例，设置更长的超时时间
            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setConnectTimeout(30000); // 连接超时时间30秒
            factory.setReadTimeout(120000);   // 读取超时时间120秒
            RestTemplate customRestTemplate = new RestTemplate(factory);
            
            // 发送HTTP请求
            log.info("同步车型数据到SAAS系统，请求URL: {}", Global.instance.saasModelSyncUrl);
            log.info("同步车型数据到SAAS系统，请求参数: {}", JSONUtil.toJsonStr(params));
            HttpEntity<String> httpEntity = new HttpEntity<>(JSONUtil.toJsonStr(params), headers);
            ResponseEntity<JSONObject> responseEntity = customRestTemplate.postForEntity(
                    Global.instance.saasModelSyncUrl, httpEntity, JSONObject.class);
            
            // 处理响应结果
            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                JSONObject responseBody = responseEntity.getBody();
                if (responseBody != null && responseBody.getInteger("code") == 200) {
                    log.info("车型批次同步成功，共 {} 条数据", batch.size());
                    return true;
                } else {
                    log.error("车型批次同步失败！");
                    return false;
                }
            } else {
                log.error("车型批次同步失败，HTTP请求失败，状态码: {}", responseEntity.getStatusCodeValue());
                return false;
            }
        } catch (Exception e) {
            log.error("车型批次同步过程中发生异常", e);
            return false;
        }
    }

    /**
     * 获取需要同步到SAAS系统的车型数据
     * @param lastId 上次查询的最后ID
     * @param isFullSync 是否全量同步
     * @return 车型数据列表
     */
    private List<Map<String, Object>> getVehicleModelSyncDataForSaas(Long lastId, boolean isFullSync) {
        log.info("获取需要同步到SAAS系统的车型数据，lastId={}，同步模式：{}", lastId, isFullSync ? "全量同步" : "增量同步");
        
        // 查询当前批次数据
        List<VehicleModelInfo> batchModelList = tableVehicleModelInfoService.selectSyncSaasVehicleModelInfo(lastId, 1000, isFullSync);
        
        if (CollectionUtil.isEmpty(batchModelList)) {
            return Collections.emptyList();
        }
        
        // 转换当前批次数据并返回
        return batchModelList.stream()
                .map(this::convertModelToSaasFormat)
                .collect(Collectors.toList());
    }

    /**
     * 将车型信息转换为SAAS系统需要的格式
     */
    private Map<String, Object> convertModelToSaasFormat(VehicleModelInfo modelInfo) {
        Map<String, Object> map = new HashMap<>();
        // 基本信息
        map.put("id", modelInfo.getId());
        map.put("vehicleModelName", modelInfo.getVehicleModelName());
        return map;
    }
}
