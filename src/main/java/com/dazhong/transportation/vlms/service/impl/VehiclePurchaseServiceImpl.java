package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.dazhong.transportation.vlms.config.DingTalkConfig;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.constant.DingTalkConstant;
import com.dazhong.transportation.vlms.database.*;
import com.dazhong.transportation.vlms.dto.*;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.*;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.*;
import com.dazhong.transportation.vlms.excel.ImportPurchaseIntention;
import com.dazhong.transportation.vlms.excel.ImportVehicleReceiving;
import com.dazhong.transportation.vlms.excel.util.ExcelUtil;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.*;
import com.dazhong.transportation.vlms.service.IDataDictService;
import com.dazhong.transportation.vlms.service.IDingTalkService;
import com.dazhong.transportation.vlms.service.ILicensePlateQuotaService;
import com.dazhong.transportation.vlms.service.IVehiclePurchaseService;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-01-03 10:50
 */
@Slf4j
@Service
public class VehiclePurchaseServiceImpl implements IVehiclePurchaseService {

    @Autowired
    private TablePurchaseIntentionService tablePurchaseIntentionService;

    @Autowired
    private TablePurchaseApplyService tablePurchaseApplyService;

    @Autowired
    private TablePurchaseApplyDetailsService tablePurchaseApplyDetailsService;

    @Autowired
    private TableApplyFileService tableApplyFileService;

    @Autowired
    private TablePurchaseEquipmentService tablePurchaseEquipmentService;

    @Autowired
    private TableVehicleOrderReceiptService tableVehicleOrderReceiptService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Autowired
    private TableVehicleService tableVehicleService;

    @Autowired
    private TableVehicleManagementLegacyService tableVehicleManagementLegacyService;

    @Autowired
    private ILicensePlateQuotaService licensePlateQuotaService;

    @Autowired
    private TableVehicleModelInfoService tableVehicleModelInfoService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Autowired
    private IDingTalkService dingTalkService;

    @Autowired
    private IDataDictService dataDictService;

    @Autowired
    private DingTalkConfig dingTalkConfig;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public PageResponse searchPurchaseIntention(SearchPurchaseIntentionRequest request, TokenUserInfo tokenUserInfo) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehiclePurchaseIntention> list = tablePurchaseIntentionService.queryPurchaseIntention(request, tokenUserInfo.getOrgIdList());
        PageInfo<VehiclePurchaseIntention> pageInfo = new PageInfo<>(list);
        // 返回结果
        List<PurchaseIntentionResponse> responseList = new ArrayList<>();
        list.forEach(res -> {
            PurchaseIntentionResponse response = BeanUtil.copyProperties(res, PurchaseIntentionResponse.class);
            long purchaseApplyId = res.getPurchaseApplyId();
            if (purchaseApplyId > 0) {
                VehiclePurchaseApply vehiclePurchaseApply = tablePurchaseApplyService.selectById(purchaseApplyId);
                response.setPurchaseApplyNo(vehiclePurchaseApply.getPurchaseApplyNo());
                response.setPurchaseApplyStatus(String.valueOf(vehiclePurchaseApply.getPurchaseApplyStatus()));
            }
            responseList.add(response);
        });
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return pageResponse;
    }

    @Override
    public ResultResponse queryPurchaseIntention(String intentionNo) {
        VehiclePurchaseIntention intention = tablePurchaseIntentionService.queryPurchaseIntention(intentionNo);
        if (intention == null) {
            return ResultResponse.businessFailed("未查询到采购意向信息");
        }
        PurchaseIntentionResponse response = BeanUtil.copyProperties(intention, PurchaseIntentionResponse.class);
        VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(intention.getVehicleModelId());
        if (vehicleModelInfo != null) {
            response.setPriceOnAutohome(vehicleModelInfo.getPriceOnAutohome());
        }
        return ResultResponse.success(response);
    }

    @Override
    public ResultResponse importPurchaseIntention(BaseImportFileUrlRequest request, TokenUserInfo tokenUserInfo) {
        List<ImportPurchaseIntention> readList = ExcelUtil.read(request.getFilePath(), 0, ImportPurchaseIntention.class);
        if (ObjectUtil.isEmpty(readList)) {
            throw new ServiceException("导入文件内容不能为空");
        }

        // 车架号去重
        List<String> contractNoList = readList.stream().map(ImportPurchaseIntention::getContractNo).distinct().collect(Collectors.toList());
        if (contractNoList.size() != readList.size()) {
            throw new ServiceException("合同号不能重复");
        }

        // 查询区域列表
        DataDictResponse<Integer> areaResponse = dataDictService.queryDataMaintainDict("area");
        List<DataDictDto<Integer>> areatList = areaResponse.getDataDictList();
        Map<String, DataDictDto<Integer>> areaMap = areatList.stream().collect(Collectors.toMap(DataDictDto::getName, dataDictDto -> dataDictDto));
        // 车型列表
        Map<String, VehicleModelInfo> vehicleModelInfoMap = tableVehicleModelInfoService.getAllVehicleModelMapByName();

        // 查询是否营运/营运类别/号牌种类/车身颜色/合同类型/获取方式/使用性质/车辆拥有公司/供应商/组织机构
        List<String> systemCodeList = Arrays.asList(
                DataDictEnum.OPERATE_TYPE.getValue(),
                DataDictEnum.VEHICLE_COLOR.getValue());
        Map<String, Map<String, Integer>> dataDictMap = dataDictService.getDataMaintainDictValueMap(systemCodeList);
        Map<String, Integer> operateTypeMap = dataDictMap.get(DataDictEnum.OPERATE_TYPE.getValue());
        Map<String, Integer> vehicleColorMap = dataDictMap.get(DataDictEnum.VEHICLE_COLOR.getValue());

        /*参数校验*/
        for (ImportPurchaseIntention importPurchaseIntention : readList) {
            VehiclePurchaseIntention intention = tablePurchaseIntentionService.queryByContractNo(importPurchaseIntention.getContractNo());
            if (intention != null) {
                throw new ServiceException(StrUtil.format("{}-合同号已存在", importPurchaseIntention.getContractNo()));
            }
            if (StringUtils.isNotBlank(importPurchaseIntention.getVehicleBodyColor()) &&
                    vehicleColorMap.get(importPurchaseIntention.getVehicleBodyColor()) == null) {
                throw new ServiceException(StrUtil.format("{}-车身颜色参数错误", importPurchaseIntention.getVehicleBodyColor()));
            }

            if (ObjectUtil.isNotEmpty(importPurchaseIntention.getVehicleModelName())) {
                if (vehicleModelInfoMap.get(importPurchaseIntention.getVehicleModelName()) == null) {
                    throw new ServiceException(StrUtil.format("{}-车型名称参数错误", importPurchaseIntention.getVehicleModelName()));
                }
            }
            if (ObjectUtil.isNotEmpty(importPurchaseIntention.getLicensePlateLocation())) {
                if (areaMap.get(importPurchaseIntention.getLicensePlateLocation()) == null) {
                    throw new ServiceException(StrUtil.format("{}-合同约定牌照所属地参数错误", importPurchaseIntention.getLicensePlateLocation()));
                }
            }
            if (StringUtils.isNotBlank(importPurchaseIntention.getLicensePlateAttribute()) &&
                    operateTypeMap.get(importPurchaseIntention.getLicensePlateAttribute()) == null) {
                throw new ServiceException(StrUtil.format("{}-合同约定牌照属性参数错误", importPurchaseIntention.getLicensePlateAttribute()));
            }
        }

        for (ImportPurchaseIntention intention : readList) {
            String intentionNo = redisUtils.generateUniqueId("YX");
            VehiclePurchaseIntention vehiclePurchaseIntention = BeanUtil.copyProperties(intention, VehiclePurchaseIntention.class);
            vehiclePurchaseIntention.setIntentionNo(intentionNo);
            vehiclePurchaseIntention.setOrgName(intention.getAssetOwner());
            if (ObjectUtil.isNotEmpty(intention.getVehicleModelName())) {
                VehicleModelInfo vehicleModelInfo = vehicleModelInfoMap.get(intention.getVehicleModelName());
                if (vehicleModelInfo != null) {
                    vehiclePurchaseIntention.setVehicleModelId(vehicleModelInfo.getId());
                }
            }
            tablePurchaseIntentionService.insertPurchaseIntention(vehiclePurchaseIntention);
        }
        return ResultResponse.success();
    }

    @Override
    public PageResponse searchPurchaseApply(SearchPurchaseApplyRequest request, TokenUserInfo tokenUserInfo) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehiclePurchaseApply> list = tablePurchaseApplyService.searchPurchaseList(request, tokenUserInfo);
        PageInfo<VehiclePurchaseApply> pageInfo = new PageInfo<>(list);
        // 返回结果
        List<PurchaseApplyResponse> responseList = new ArrayList<>();
        Map<Integer, DataOwnerInfo> ownerInfoMap = dataDictService.getOwnerMap();
        //查询字典表相关信息
        List<String> systemCodeList = Arrays.asList(
                //组织机构
                DataDictEnum.ORGANIZATION.getValue()
        );
        Map<String, Map<Integer, String>> dataMaintainDictMap = dataDictService.getDataMaintainDictMap(systemCodeList);
        Map<Integer, String> orgMap = dataMaintainDictMap.get(DataDictEnum.ORGANIZATION.getValue());
        list.forEach(applyResponse -> {
            PurchaseApplyResponse response = BeanUtil.copyProperties(applyResponse, PurchaseApplyResponse.class);
            if (applyResponse.getOwnerId() != null && ownerInfoMap.get(applyResponse.getOwnerId()) != null) {
                response.setOwnerName(ownerInfoMap.get(applyResponse.getOwnerId()).getName());
            }
            if (applyResponse.getApplyOrgId() != null && orgMap.get(applyResponse.getApplyOrgId().intValue()) != null) {
                response.setApplyOrgName(orgMap.get(applyResponse.getApplyOrgId().intValue()));
            }
            responseList.add(response);
        });
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return pageResponse;
    }

    @Override
    public PurchaseApplyDetailResponse queryPurchaseApplyDetail(String applyNo) {
        VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApply(applyNo);
        if (purchaseApply == null) {
            throw new ServiceException("未查询到采购申请信息");
        }
        Map<Integer, DataOwnerInfo> ownerInfoMap = dataDictService.getOwnerMap();

        // 采购申请基本信息
        PurchaseApplyDetailResponse response = new PurchaseApplyDetailResponse();
        response.setId(purchaseApply.getId());
        response.setPurchaseApplyNo(purchaseApply.getPurchaseApplyNo());
        response.setApprovalNumber(purchaseApply.getApprovalNumber());
        response.setPurchaseApplyStatus(purchaseApply.getPurchaseApplyStatus());
        response.setApplyOrgId(purchaseApply.getApplyOrgId());
        response.setApplyName(purchaseApply.getApplyName());
        response.setApplyRemark(purchaseApply.getApplyRemark());
        if (StringUtils.isNotBlank(purchaseApply.getApplicantDepartmentCode())){
            response.setOriginatorDeptId(Long.valueOf(purchaseApply.getApplicantDepartmentCode()));
        }
        response.setOriginatorDeptName(purchaseApply.getApplicantDepartmentName());
        OrgInfo orgInfo = tableOrgInfoService.queryOrgInfoById(purchaseApply.getApplyOrgId());
        if (orgInfo != null) {
            response.setApplyOrgName(orgInfo.getCompanyName());
        }
        if (purchaseApply.getOwnerId() != null && ownerInfoMap.get(purchaseApply.getOwnerId()) != null) {
            response.setOwnerName(ownerInfoMap.get(purchaseApply.getOwnerId()).getName());
        }
        response.setOwnerId(purchaseApply.getOwnerId());
        response.setApplyUser(purchaseApply.getApplyUser());
        response.setPurchaseQuantity(purchaseApply.getPurchaseQuantity());
        response.setTakeDeliveryQuantity(purchaseApply.getTakeDeliveryQuantity());
        response.setApplyProductLine(purchaseApply.getApplyProductLine());
        response.setSubscriptionCompanyName(purchaseApply.getSubscriptionCompanyName());
        response.setSubscriptionCompanyCode(purchaseApply.getSubscriptionCompanyCode());
        response.setIntentionNo(purchaseApply.getIntentionNo());
        response.setSupplierType(purchaseApply.getSupplierType());
        // 查询采购意向
        VehiclePurchaseIntention purchaseIntention = tablePurchaseIntentionService.queryPurchaseIntention(purchaseApply.getIntentionNo());
        if (purchaseIntention != null) {
            PurchaseIntentionResponse intentionResponse = BeanUtil.copyProperties(purchaseIntention, PurchaseIntentionResponse.class);
            response.setIntentionResponse(intentionResponse);
        }

        // 车型信息
        Map<Long, VehicleModelInfo> vehicleModelMap = tableVehicleModelInfoService.getAllVehicleModelMap();
        // 查询采购详情
        List<VehiclePurchaseApplyDetails> detailList = tablePurchaseApplyDetailsService.queryPurchaseDetailsList(purchaseApply.getId());
        if (CollectionUtil.isNotEmpty(detailList)) {
            List<PurchaseApplyDetailDto> applyDetailList = BeanUtil.copyToList(detailList, PurchaseApplyDetailDto.class);
            applyDetailList.forEach(detail -> {
                VehicleModelInfo vehicleModelInfo = vehicleModelMap.get(detail.getPurchaseModelId());
                if (vehicleModelInfo != null) {
                    detail.setPurchaseModelName(vehicleModelInfo.getVehicleModelName());
                    detail.setPriceOnAutohome(vehicleModelInfo.getPriceOnAutohome());
                }
            });
            response.setApplyDetailList(applyDetailList);
        }
        // 查询采购设备
        List<VehiclePurchaseEquipment> equipmentList = tablePurchaseEquipmentService.queryPurchaseEquipmentList(purchaseApply.getId());
        if (CollectionUtil.isNotEmpty(equipmentList)) {
            List<PurchaseEquipmentDto> equipmentDtoList = BeanUtil.copyToList(equipmentList, PurchaseEquipmentDto.class);
            response.setEquipmentList(equipmentDtoList);
        }

        // 查询附件
        List<VehicleApplyFile> attachmentList = tableApplyFileService.queryFileList(purchaseApply.getId(), 1);
        if (CollectionUtil.isNotEmpty(attachmentList)) {
            List<VehicleApplyFileDto> fileDtoList = BeanUtil.copyToList(attachmentList, VehicleApplyFileDto.class);
            String mfsRootPath = Global.instance.mfsRootPath;
            String mfsUrl = Global.instance.mfsUrl;
            // 拼接图片地址
            fileDtoList.forEach(file -> {
                String fileUrl = file.getFileUrl().replace(mfsRootPath + "/", "");
                file.setFileUrl(StrUtil.format("{}/{}", mfsUrl, fileUrl));
            });
            response.setFileList(fileDtoList);
        }

        // 查询收货信息
        List<VehicleReceivingDto> receivingList = tableVehicleOrderReceiptService.qeuryVehicleOrderReceiptList(purchaseApply.getId());
        if (CollectionUtil.isNotEmpty(receivingList)) {
            response.setReceivingList(receivingList);
        }
        return response;
    }

    @Override
    public ResultResponse queryPurchaseOccupiedList(String applyNo) {
        VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApply(applyNo);
        if (purchaseApply == null) {
            return ResultResponse.businessFailed("未查询到采购申请信息");
        }
        // 采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
        int purchaseApplyStatus = purchaseApply.getPurchaseApplyStatus();
        if (purchaseApplyStatus != 3) {
            return ResultResponse.businessFailed("采购申请状态为审批通过才能释放");
        }
        List<PurchaseOccupiedResponse> occupiedList = new ArrayList<>();
        List<VehiclePurchaseApplyDetails> detailList = tablePurchaseApplyDetailsService.queryPurchaseDetailsList(purchaseApply.getId());
        for (VehiclePurchaseApplyDetails applyDetails : detailList) {
            // 是否占用管控额度 1-是 2-否
            int isQuotaOccupied = applyDetails.getIsQuotaOccupied();
            if (isQuotaOccupied == 1) {
                PurchaseOccupiedResponse occupiedResponse = new PurchaseOccupiedResponse();
                occupiedResponse.setApplyDetailsNo(applyDetails.getApplyDetailsNo());
                occupiedResponse.setQuotaType(applyDetails.getQuotaType());
                occupiedResponse.setQuotaAssetOwnership(applyDetails.getQuotaAssetOwnership());
                occupiedResponse.setPreOccupied(applyDetails.getPreOccupied());
                occupiedList.add(occupiedResponse);
            }
        }
        return ResultResponse.success(occupiedList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse releasePurchaseOccupied(ReleasePurchaseOccupiedRequest request, TokenUserInfo tokenUserInfo) {
        String applyNo = request.getApplyNo();
        VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApply(applyNo);
        if (purchaseApply == null) {
            return ResultResponse.businessFailed("未查询到采购申请信息");
        }
        int purchaseApplyStatus = purchaseApply.getPurchaseApplyStatus();
        if (purchaseApplyStatus != 3) {
            return ResultResponse.businessFailed("采购申请状态为审批通过才能释放");
        }
        List<VehiclePurchaseApplyDetails> detailList = tablePurchaseApplyDetailsService.queryPurchaseDetailsList(purchaseApply.getId());
        if (CollectionUtil.isEmpty(detailList)) {
            return ResultResponse.businessFailed("未查询到采购申请明细信息");
        }
        Map<String, VehiclePurchaseApplyDetails> applyDetailsMap = detailList.stream().collect(Collectors.toMap(VehiclePurchaseApplyDetails::getApplyDetailsNo, details -> details));
        List<ReleasePurchaseOccupiedDto> list = request.getList();
        for (ReleasePurchaseOccupiedDto dto : list) {
            VehiclePurchaseApplyDetails details = applyDetailsMap.get(dto.getApplyDetailsNo());
            if (details == null) {
                throw new ServiceException("未查询到采购申请明细信息");
            }
            // 是否占用管控额度 1-是 2-否
            Integer isQuotaOccupied = details.getIsQuotaOccupied();
            if (isQuotaOccupied == null || isQuotaOccupied != 1) {
                throw new ServiceException("该明细没有占用管控额度");
            }
            int preOccupied = details.getPreOccupied();
            int releaseOccupied = dto.getReleaseOccupied();
            if (releaseOccupied > preOccupied) {
                throw new ServiceException("释放数量不能大于占用数量");
            }

            details.setPreOccupied(preOccupied - releaseOccupied);
            tablePurchaseApplyDetailsService.updatePurchaseApplyDetails(details);

            UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
            updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.PRE_OCCUPY.getCode());
            updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.DECREASE.getCode());
            updateQuotaDTO.setAdjustNum(releaseOccupied);
            updateQuotaDTO.setQuotaType(details.getQuotaType());
            updateQuotaDTO.setAssetCompanyId(details.getQuotaAssetOwnership());
            licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo, true);
        }
        purchaseApply.setPreOccupied(purchaseApply.getPreOccupied() - request.getList().stream().mapToInt(ReleasePurchaseOccupiedDto::getReleaseOccupied).sum());
        tablePurchaseApplyService.updatePurchaseApply(purchaseApply, tokenUserInfo);

        tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "车辆采购申请单-操作采购单预占用释放操作", tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    public ResultResponse queryApplyDetailsNoList(String applyNo) {
        VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApply(applyNo);
        if (purchaseApply == null) {
            throw new ServiceException("未查询到采购申请信息");
        }
        List<VehiclePurchaseApplyDetails> detailList = tablePurchaseApplyDetailsService.queryPurchaseDetailsList(purchaseApply.getId());
        if (CollectionUtil.isNotEmpty(detailList)) {
            List<String> applyDetailsNoList = detailList.stream().map(VehiclePurchaseApplyDetails::getApplyDetailsNo).collect(Collectors.toList());
            return ResultResponse.success(applyDetailsNoList);
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse addPurchaseApply(AddPurchaseApplyRequest request, TokenUserInfo tokenUserInfo) {
        //采购意向单号 判断创建来源
        String intentionNo = request.getIntentionNo();
        VehiclePurchaseIntention purchaseIntention = null;
        if (StringUtils.isNotBlank(intentionNo)) {
            purchaseIntention = tablePurchaseIntentionService.queryPurchaseIntention(intentionNo);
            if (purchaseIntention == null) {
                return ResultResponse.businessFailed("未查询到采购意向信息");
            }
            //是否有单号 1-是 2-否
            int applyStatus = purchaseIntention.getApplyStatus();
            if (applyStatus == 1) {
                return ResultResponse.businessFailed("该采购意向单号已创建采购申请单");
            }
        }
        // 操作类型 1-保存 2-提交
        int operateType = request.getOperateType();
        List<PurchaseEquipmentDto> equipmentList = request.getEquipmentList();
        // 新增采购详情
        List<PurchaseApplyDetailDto> applyDetailList = request.getApplyDetailList();
        // 统计applyDetailList里的 quantity总数
        if (operateType == 2) {
            for (PurchaseApplyDetailDto dto : applyDetailList) {
                if (dto.getQuantity() == null) {
                    return ResultResponse.businessFailed("采购详情数量不能为空");
                }
            }
            int totalQuantity = applyDetailList.stream()
                    .mapToInt(PurchaseApplyDetailDto::getQuantity)
                    .sum();
            if (totalQuantity > request.getPurchaseQuantity()) {
                return ResultResponse.businessFailed("采购明细数量不能大于采购数量");
            }
        }

        int preOccupied = 0;
        for (PurchaseApplyDetailDto dto : applyDetailList) {
            Integer quantity = dto.getQuantity();
            Integer isQuotaOccupied = dto.getIsQuotaOccupied();
            if (isQuotaOccupied != null && isQuotaOccupied == 1 && quantity != null) {
                preOccupied += dto.getQuantity();
            }
        }
        // 新增采购申请
        String purchaseApplyNo = redisUtils.generateUniqueId("PR");
        VehiclePurchaseApply purchaseApply = BeanUtil.copyProperties(request, VehiclePurchaseApply.class);
        purchaseApply.setPurchaseApplyNo(purchaseApplyNo);
        purchaseApply.setPurchaseApplyStatus(BizConstant.ApplyStatus.applyStatus_1);
        purchaseApply.setSubmitDate(operateType == 1 ? null : new Date());
        purchaseApply.setPreOccupied(preOccupied);
        purchaseApply.setVehicleOccupied(0);
        purchaseApply.setSupplierType(request.getSupplierType());
        purchaseApply.setApplicantDepartmentCode(String.valueOf(request.getOriginatorDeptId()));
        purchaseApply.setApplicantDepartmentName(request.getOriginatorDeptName());
        tablePurchaseApplyService.savePurchaseApply(purchaseApply, tokenUserInfo);

        for (PurchaseApplyDetailDto dto : applyDetailList) {
            BigDecimal unitPrice = dto.getUnitPrice();
            Integer quantity = dto.getQuantity();
            if (unitPrice != null && quantity != null) {
                dto.setTotalPrice(unitPrice.multiply(new BigDecimal(quantity)));
            }
            BigDecimal otherCosts = dto.getOtherCosts();
            if (otherCosts != null && dto.getTotalPrice() != null) {
                dto.setTotalPrice(dto.getTotalPrice().add(otherCosts));
            }
            VehiclePurchaseApplyDetails applyDetails = BeanUtil.copyProperties(dto, VehiclePurchaseApplyDetails.class);
            applyDetails.setPreOccupied(0);
            applyDetails.setPurchaseApplyId(purchaseApply.getId());
            String applyDetailsNo = redisUtils.generateUniqueId("PO");
            applyDetails.setApplyDetailsNo(applyDetailsNo);
            Integer isQuotaOccupied = dto.getIsQuotaOccupied();
            if (isQuotaOccupied != null && isQuotaOccupied == 1 && quantity != null) {
                applyDetails.setPreOccupied(quantity);
            }
            tablePurchaseApplyDetailsService.savePurchaseApplyDetails(applyDetails);
        }
        // 新增车载设备
        if (CollectionUtil.isNotEmpty(equipmentList)) {
            for (PurchaseEquipmentDto dto : equipmentList) {
                BigDecimal unitPrice = dto.getUnitPrice();
                Integer quantity = dto.getQuantity();
                if (unitPrice != null && quantity != null) {
                    dto.setTotalPrice(unitPrice.multiply(new BigDecimal(quantity)));
                }
                BigDecimal otherCosts = dto.getOtherCosts();
                if (otherCosts != null) {
                    dto.setTotalPrice(dto.getTotalPrice().add(otherCosts));
                }
                VehiclePurchaseEquipment equipment = BeanUtil.copyProperties(dto, VehiclePurchaseEquipment.class);
                equipment.setPurchaseApplyId(purchaseApply.getId());
                tablePurchaseEquipmentService.savePurchaseEquipment(equipment);
            }
        }
        // 新增采购附件
        List<VehicleApplyFileDto> fileList = request.getFileList();
        if (CollectionUtil.isNotEmpty(fileList)) {
            List<VehicleApplyFile> applyFileList = BeanUtil.copyToList(fileList, VehicleApplyFile.class);
            for (VehicleApplyFile vehicleApplyFile : applyFileList) {
                vehicleApplyFile.setForeignId(purchaseApply.getId());
                vehicleApplyFile.setBusinessType(FileBusinessTypeEnum.PURCHASE_APPLICATION.getCode());
                tableApplyFileService.insert(vehicleApplyFile, tokenUserInfo);
            }
        }

        // 更新采购意向关联信息
        if (purchaseIntention != null) {
            purchaseIntention.setApplyStatus(1);
            purchaseIntention.setPurchaseApplyId(purchaseApply.getId());
            tablePurchaseIntentionService.updatePurchaseIntention(purchaseIntention);
        }
        // 调用占用上牌额度服务
        for (PurchaseApplyDetailDto detailDto : applyDetailList) {
            //是否占用管控额度 1-是 2-否
            int isQuotaOccupied = detailDto.getIsQuotaOccupied() == null ? 2 : detailDto.getIsQuotaOccupied();
            if (isQuotaOccupied != 1) {
                continue;
            }
            UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
            updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.INCREASE.getCode());
            updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.PRE_OCCUPY.getCode());
            updateQuotaDTO.setAdjustNum(detailDto.getQuantity());
            updateQuotaDTO.setQuotaType(detailDto.getQuotaType());
            updateQuotaDTO.setAssetCompanyId(detailDto.getQuotaAssetOwnership());
            licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo, true);
        }

        // 提交钉钉审批
        if (operateType == 2) {
            CreateDingTalkWorkFlowRequest createDingTalkWorkFlowRequest = getDingTalkWorkFlow(purchaseApply,applyDetailList);
            createDingTalkWorkFlowRequest.setOriginatorDeptId(request.getOriginatorDeptId());
            if (CollectionUtil.isNotEmpty(fileList)) {
                CommitDingFlowAttachmentDto attachmentInfo = new CommitDingFlowAttachmentDto();
                attachmentInfo.setName("附件");
                List<DingFlowAttachmentFileInfoDto> attachmentFileList = new ArrayList<>();
                String mfsRootPath = Global.instance.mfsRootPath;
                for (VehicleApplyFileDto dto : fileList) {
                    DingFlowAttachmentFileInfoDto fileInfoDto = new DingFlowAttachmentFileInfoDto();
                    String fileUrl = mfsRootPath + "/" + dto.getFileUrl();
                    File file = new File(fileUrl);
                    fileInfoDto.setFile(file);
                    fileInfoDto.setFileName(StrUtil.format("{}-{}", PurchaseFileTypeEnum.getDesc(dto.getFileType()), file.getName()));
                    attachmentFileList.add(fileInfoDto);
                }
                attachmentInfo.setAttachmentFileList(attachmentFileList);
                createDingTalkWorkFlowRequest.setAttachmentInfo(attachmentInfo);
            }
            try {
                createDingTalkWorkFlowRequest.setOriginatorUserId(tokenUserInfo.getDingTalkNum());
                if (purchaseIntention != null && StringUtils.isNotBlank(purchaseIntention.getDingTalkId())){
                    createDingTalkWorkFlowRequest.setOriginatorUserId(purchaseIntention.getDingTalkId());
                }
                String dingTalkNo = dingTalkService.createDingTalkWorkFlow(createDingTalkWorkFlowRequest);
                purchaseApply.setApprovalNumber(dingTalkNo);
                purchaseApply.setPurchaseApplyStatus(BizConstant.ApplyStatus.applyStatus_2);
                tablePurchaseApplyService.updatePurchaseApply(purchaseApply, tokenUserInfo);
            } catch (ServiceException e) {
                log.error("提交钉钉审批单异常", e);
                throw new ServiceException(StrUtil.format("提交采购审批单-{}", e.getMessage()));
            }
        }
        // 添加日志
        tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "创建车辆采购申请单", tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse updatePurchaseApply(UpdatePurchaseApplyRequest request, TokenUserInfo tokenUserInfo) {
        String purchaseApplyNo = request.getPurchaseApplyNo();
        VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApply(purchaseApplyNo);
        if (purchaseApply == null) {
            return ResultResponse.businessFailed("未查询到采购申请信息");
        }
        /*未提交、审批拒绝 可以编辑*/
        //采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
        int purchaseApplyStatus = purchaseApply.getPurchaseApplyStatus();
        if (purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_1 &&
                purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_4) {
            return ResultResponse.businessFailed("采购申请状态不满足条件，不能编辑");
        }
        // 操作类型 1-保存 2-提交
        int operateType = request.getOperateType();
        List<PurchaseEquipmentDto> equipmentList = request.getEquipmentList();
        List<PurchaseApplyDetailDto> applyDetailList = request.getApplyDetailList();
        // 统计applyDetailList里的 quantity总数
        if (operateType == 2) {
            for (PurchaseApplyDetailDto dto : applyDetailList) {
                if (dto.getQuantity() == null) {
                    return ResultResponse.businessFailed("采购详情数量不能为空");
                }
            }
            int totalQuantity = applyDetailList.stream()
                    .mapToInt(PurchaseApplyDetailDto::getQuantity)
                    .sum();
            if (totalQuantity > request.getPurchaseQuantity()) {
                return ResultResponse.businessFailed("采购明细数量不能大于采购数量");
            }
        }
        int preOccupied = 0;
        for (PurchaseApplyDetailDto dto : applyDetailList) {
            Integer quantity = dto.getQuantity();
            Integer isQuotaOccupied = dto.getIsQuotaOccupied();
            if (isQuotaOccupied != null && isQuotaOccupied == 1 && quantity != null) {
                preOccupied += dto.getQuantity();
            }
        }
        // 更新采购申请信息
        purchaseApply.setPurchaseApplyStatus(BizConstant.ApplyStatus.applyStatus_1);
        purchaseApply.setSubmitDate(operateType == 1 ? null : new Date());
        purchaseApply.setApplyName(request.getApplyName());
        purchaseApply.setPurchaseQuantity(request.getPurchaseQuantity());
        purchaseApply.setApplyOrgId(request.getApplyOrgId());
        purchaseApply.setSubscriptionCompanyCode(request.getSubscriptionCompanyCode());
        purchaseApply.setSubscriptionCompanyName(request.getSubscriptionCompanyName());
        purchaseApply.setApplyRemark(request.getApplyRemark());
        purchaseApply.setApplyProductLine(request.getApplyProductLine());
        purchaseApply.setOwnerId(request.getOwnerId());
        purchaseApply.setPreOccupied(preOccupied);
        purchaseApply.setVehicleOccupied(0);
        purchaseApply.setSupplierType(request.getSupplierType());
        purchaseApply.setApplicantDepartmentCode(String.valueOf(request.getOriginatorDeptId()));
        purchaseApply.setApplicantDepartmentName(request.getOriginatorDeptName());
        tablePurchaseApplyService.updatePurchaseApply(purchaseApply, tokenUserInfo);

        // 历史采购详情
        List<VehiclePurchaseApplyDetails> oldDetailList = tablePurchaseApplyDetailsService.queryPurchaseDetailsList(purchaseApply.getId());

        // 删除采购详情 重新添加
        tablePurchaseApplyDetailsService.deletePurchaseApplyDetails(purchaseApply.getId());
        for (PurchaseApplyDetailDto dto : applyDetailList) {
            BigDecimal unitPrice = dto.getUnitPrice();
            Integer quantity = dto.getQuantity();
            if (unitPrice != null && quantity != null) {
                dto.setTotalPrice(unitPrice.multiply(new BigDecimal(quantity)));
            }
            BigDecimal otherCosts = dto.getOtherCosts();
            if (otherCosts != null && dto.getTotalPrice() != null) {
                dto.setTotalPrice(dto.getTotalPrice().add(otherCosts));
            }
            VehiclePurchaseApplyDetails applyDetails = BeanUtil.copyProperties(dto, VehiclePurchaseApplyDetails.class);
            applyDetails.setPreOccupied(0);
            applyDetails.setPurchaseApplyId(purchaseApply.getId());
            String applyDetailsNo = redisUtils.generateUniqueId("PO");
            applyDetails.setApplyDetailsNo(applyDetailsNo);
            Integer isQuotaOccupied = dto.getIsQuotaOccupied();
            if (isQuotaOccupied != null && isQuotaOccupied == 1 && quantity != null) {
                applyDetails.setPreOccupied(quantity);
            }
            tablePurchaseApplyDetailsService.savePurchaseApplyDetails(applyDetails);
        }

        /* 未提交的审批 重新编辑后 调用占用上牌额度服务-归还上牌额度*/
        if (purchaseApplyStatus == BizConstant.ApplyStatus.applyStatus_1){
            for (VehiclePurchaseApplyDetails detailDto : oldDetailList) {
                //是否占用管控额度 1-是 2-否
                int isQuotaOccupied = detailDto.getIsQuotaOccupied() == null ? 2 : detailDto.getIsQuotaOccupied();
                if (isQuotaOccupied != 1) {
                    continue;
                }
                UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
                updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.PRE_OCCUPY.getCode());
                updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.DECREASE.getCode());
                updateQuotaDTO.setAdjustNum(detailDto.getQuantity());
                updateQuotaDTO.setQuotaType(detailDto.getQuotaType());
                updateQuotaDTO.setAssetCompanyId(detailDto.getQuotaAssetOwnership());
                licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo, true);
            }
        }

        // 删除车载设备 重新添加
        tablePurchaseEquipmentService.deletePurchaseEquipment(purchaseApply.getId());
        // 车载设备（巡网）列表
        if (CollectionUtil.isNotEmpty(equipmentList)) {
            for (PurchaseEquipmentDto dto : equipmentList) {
                BigDecimal unitPrice = dto.getUnitPrice();
                Integer quantity = dto.getQuantity();
                if (unitPrice != null && quantity != null) {
                    dto.setTotalPrice(unitPrice.multiply(new BigDecimal(quantity)));
                }
                BigDecimal otherCosts = dto.getOtherCosts();
                if (otherCosts != null && dto.getTotalPrice() != null) {
                    dto.setTotalPrice(dto.getTotalPrice().add(otherCosts));
                }
                VehiclePurchaseEquipment equipment = BeanUtil.copyProperties(dto, VehiclePurchaseEquipment.class);
                equipment.setPurchaseApplyId(purchaseApply.getId());
                tablePurchaseEquipmentService.savePurchaseEquipment(equipment);
            }
        }

        tableApplyFileService.deleteApplyFile(purchaseApply.getId(), 1);
        // 附件列表
        List<VehicleApplyFileDto> fileList = request.getFileList();
        if (CollectionUtil.isNotEmpty(fileList)) {
            String mfsUrl = Global.instance.mfsUrl;
            // 删除采购附件 重新添加
            List<VehicleApplyFile> applyFileList = BeanUtil.copyToList(fileList, VehicleApplyFile.class);
            for (VehicleApplyFile applyFile : applyFileList) {
                String fileUrl = applyFile.getFileUrl();
                if (fileUrl.contains(mfsUrl)) {
                    applyFile.setFileUrl(fileUrl.replace(mfsUrl + "/", ""));
                }
                applyFile.setForeignId(purchaseApply.getId());
                applyFile.setBusinessType(FileBusinessTypeEnum.PURCHASE_APPLICATION.getCode());
                tableApplyFileService.insert(applyFile, tokenUserInfo);
            }
        }

        // 调用占用上牌额度服务
        for (PurchaseApplyDetailDto detailDto : applyDetailList) {
            //是否占用管控额度 1-是 2-否
            int isQuotaOccupied = detailDto.getIsQuotaOccupied() == null ? 2 : detailDto.getIsQuotaOccupied();
            if (isQuotaOccupied != 1) {
                continue;
            }
            UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
            updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.PRE_OCCUPY.getCode());
            updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.INCREASE.getCode());
            updateQuotaDTO.setAdjustNum(detailDto.getQuantity());
            updateQuotaDTO.setQuotaType(detailDto.getQuotaType());
            updateQuotaDTO.setAssetCompanyId(detailDto.getQuotaAssetOwnership());
            licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo, true);
        }

        // 提交钉钉审批
        if (operateType == 2) {
            CreateDingTalkWorkFlowRequest createDingTalkWorkFlowRequest = getDingTalkWorkFlow(purchaseApply,applyDetailList);
            createDingTalkWorkFlowRequest.setOriginatorDeptId(request.getOriginatorDeptId());
            if (CollectionUtil.isNotEmpty(fileList)) {
                String mfsUrl = Global.instance.mfsUrl;
                String mfsRootPath = Global.instance.mfsRootPath;
                CommitDingFlowAttachmentDto attachmentInfo = new CommitDingFlowAttachmentDto();
                attachmentInfo.setName("附件");
                List<DingFlowAttachmentFileInfoDto> attachmentFileList = new ArrayList<>();
                for (VehicleApplyFileDto dto : fileList) {
                    DingFlowAttachmentFileInfoDto fileInfoDto = new DingFlowAttachmentFileInfoDto();
                    String fileUrl = dto.getFileUrl();
                    if (fileUrl.contains(mfsUrl)) {
                        fileUrl = StrUtil.format("{}{}", mfsRootPath, fileUrl.replace(mfsUrl, ""));
                    }
                    File file = new File(fileUrl);
                    fileInfoDto.setFile(file);
                    fileInfoDto.setFileName(StrUtil.format("{}-{}", PurchaseFileTypeEnum.getDesc(dto.getFileType()), dto.getFileName()));
                    attachmentFileList.add(fileInfoDto);
                }
                attachmentInfo.setAttachmentFileList(attachmentFileList);
                createDingTalkWorkFlowRequest.setAttachmentInfo(attachmentInfo);
            }
            try {
                createDingTalkWorkFlowRequest.setOriginatorUserId(tokenUserInfo.getDingTalkNum());
                VehiclePurchaseIntention purchaseIntention = tablePurchaseIntentionService.queryPurchaseIntention(purchaseApply.getIntentionNo());
                if (purchaseIntention != null && StringUtils.isNotBlank(purchaseIntention.getDingTalkId())){
                    createDingTalkWorkFlowRequest.setOriginatorUserId(purchaseIntention.getDingTalkId());
                }
                String dingTalkNo = dingTalkService.createDingTalkWorkFlow(createDingTalkWorkFlowRequest);
                purchaseApply.setApprovalNumber(dingTalkNo);
                purchaseApply.setPurchaseApplyStatus(BizConstant.ApplyStatus.applyStatus_2);
                tablePurchaseApplyService.updatePurchaseApply(purchaseApply, tokenUserInfo);
            } catch (ServiceException e) {
                log.error("提交钉钉审批单异常", e);
                throw new ServiceException(StrUtil.format("提交采购审批单-{}", e.getMessage()));
            }
        }
        // 添加日志
        tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "修改车辆采购申请单", tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse editPurchaseApplyStatus(EditPurchaseApplyStatusRequest request, TokenUserInfo tokenUserInfo) {
        //操作类型 1-作废 2-撤回
        int operateType = request.getOperateType();
        String purchaseApplyNo = request.getPurchaseApplyNo();
        VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApply(purchaseApplyNo);
        if (purchaseApply == null) {
            return ResultResponse.businessFailed("未查询到采购申请信息");
        }
        //采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
        int purchaseApplyStatus = purchaseApply.getPurchaseApplyStatus();
        if (purchaseApplyStatus == BizConstant.ApplyStatus.applyStatus_5) {
            return ResultResponse.success();
        }
        if (operateType == 1) {
            // 未提交、审批拒绝 可以作废
            if (purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_1 &&
                    purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_4) {
                return ResultResponse.businessFailed("采购申请状态不满足条件，无法作废");
            }
            tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "车辆采购申请单-操作作废操作", tokenUserInfo);
        } else if (operateType == 2) {
            if (purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_2) {
                return ResultResponse.businessFailed("采购申请状态不满足条件，无法撤回");
            }
            // 判断是否有收货信息
            int takeDeliveryQuantity = purchaseApply.getTakeDeliveryQuantity();
            if (takeDeliveryQuantity > 0) {
                return ResultResponse.businessFailed("采购申请已收货，无法撤回");
            }
            if (StringUtils.isBlank(purchaseApply.getApprovalNumber())) {
                return ResultResponse.businessFailed("钉钉审批单号不存在，无法撤回");
            }
            //调用钉钉撤回服务
            TerminateDingTalkProcessInstanceRequest processInstanceRequest = new TerminateDingTalkProcessInstanceRequest();
            processInstanceRequest.setSystem(true);
            processInstanceRequest.setProcessInstanceId(purchaseApply.getApprovalNumber());
            processInstanceRequest.setRemark("采购申请撤回");
            dingTalkService.terminateProcessInstance(processInstanceRequest);
            tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "车辆采购申请单-操作撤回操作", tokenUserInfo);
        }
        // 清空采购意向关联信息
        VehiclePurchaseIntention purchaseIntention = tablePurchaseIntentionService.queryPurchaseIntention(purchaseApply.getId());
        if (purchaseIntention != null) {
            purchaseIntention.setPurchaseApplyId(-1L);
            purchaseIntention.setApplyStatus(2);
            tablePurchaseIntentionService.updatePurchaseIntention(purchaseIntention);
        }
        purchaseApply.setPreOccupied(0);
        purchaseApply.setPurchaseApplyStatus(BizConstant.ApplyStatus.applyStatus_5);
        tablePurchaseApplyService.updatePurchaseApply(purchaseApply, tokenUserInfo);
        // 采购详情
        List<VehiclePurchaseApplyDetails> detailList = tablePurchaseApplyDetailsService.queryPurchaseDetailsList(purchaseApply.getId());
        // 调用占用上牌额度服务-归还上牌额度
        for (VehiclePurchaseApplyDetails detailDto : detailList) {
            //是否占用管控额度 1-是 2-否
            int isQuotaOccupied = detailDto.getIsQuotaOccupied() == null ? 2 : detailDto.getIsQuotaOccupied();
            // 占用额度数量 兼容历史数据
            int preOccupied = detailDto.getPreOccupied() == null ? detailDto.getQuantity() : detailDto.getPreOccupied();
            if (isQuotaOccupied != 1 || preOccupied == 0) {
                continue;
            }
            UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
            updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.PRE_OCCUPY.getCode());
            updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.DECREASE.getCode());
            updateQuotaDTO.setAdjustNum(preOccupied);
            updateQuotaDTO.setQuotaType(detailDto.getQuotaType());
            updateQuotaDTO.setAssetCompanyId(detailDto.getQuotaAssetOwnership());
            licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo, true);
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse closePurchaseApply(ClosePurchaseApplyRequest request, TokenUserInfo tokenUserInfo) {
        List<String> purchaseApplyNoList = request.getPurchaseApplyNoList();
        for (String purchaseApplyNo : purchaseApplyNoList) {
            VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApply(purchaseApplyNo);
            if (purchaseApply == null) {
                throw new ServiceException(StrUtil.format("{}-未查询到采购申请信息", purchaseApplyNo));
            }
            int takeDeliveryQuantity = purchaseApply.getTakeDeliveryQuantity();
            if (takeDeliveryQuantity > 0) {
                throw new ServiceException(StrUtil.format("{}-该采购申请已收货，无法关闭", purchaseApplyNo));
            }
            int vehicleOccupied = purchaseApply.getVehicleOccupied();
            if (vehicleOccupied > 0) {
                throw new ServiceException(StrUtil.format("{}-该采购申请已占用，无法关闭", purchaseApplyNo));
            }
            // 采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
            int purchaseApplyStatus = purchaseApply.getPurchaseApplyStatus();
            if (purchaseApplyStatus == BizConstant.ApplyStatus.applyStatus_6) {
                continue;
            }
            if (purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_3) {
                throw new ServiceException(StrUtil.format("{}-采购申请状态异常，不满足关闭操作", purchaseApplyNo));
            }
            // 清空采购意向关联信息
            VehiclePurchaseIntention purchaseIntention = tablePurchaseIntentionService.queryPurchaseIntention(purchaseApply.getId());
            if (purchaseIntention != null) {
                purchaseIntention.setPurchaseApplyId(-1L);
                purchaseIntention.setApplyStatus(2);
                tablePurchaseIntentionService.updatePurchaseIntention(purchaseIntention);
            }
            purchaseApply.setPurchaseApplyStatus(BizConstant.ApplyStatus.applyStatus_6);

            // 采购详情
            List<VehiclePurchaseApplyDetails> detailList = tablePurchaseApplyDetailsService.queryPurchaseDetailsList(purchaseApply.getId());
            // 调用占用上牌额度服务-归还上牌额度
            for (VehiclePurchaseApplyDetails detailDto : detailList) {
                //是否占用管控额度 1-是 2-否
                int isQuotaOccupied = detailDto.getIsQuotaOccupied() == null ? 2 : detailDto.getIsQuotaOccupied();
                int preOccupied = detailDto.getPreOccupied() == null ? detailDto.getQuantity() : detailDto.getPreOccupied();
                if (isQuotaOccupied != 1 || preOccupied == 0) {
                    continue;
                }

                detailDto.setPreOccupied(0);
                tablePurchaseApplyDetailsService.updatePurchaseApplyDetails(detailDto);

                UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
                updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.PRE_OCCUPY.getCode());
                updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.DECREASE.getCode());
                updateQuotaDTO.setAdjustNum(preOccupied);
                updateQuotaDTO.setQuotaType(detailDto.getQuotaType());
                updateQuotaDTO.setAssetCompanyId(detailDto.getQuotaAssetOwnership());
                licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo, true);
            }
            purchaseApply.setPreOccupied(0);
            tablePurchaseApplyService.updatePurchaseApply(purchaseApply, tokenUserInfo);
            tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "车辆采购申请单-操作关闭操作", tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse purchasingReceiving(PurchaseReceivingRequest request, TokenUserInfo tokenUserInfo) {
        // 判断采购信息是否存在
        VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApply(request.getPurchaseApplyNo());
        if (purchaseApply == null) {
            return ResultResponse.businessFailed("未查询到采购申请信息");
        }
        // 采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
        int purchaseApplyStatus = purchaseApply.getPurchaseApplyStatus();
        if (purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_2
                && purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_3) {
            return ResultResponse.businessFailed("采购申请状态异常，不满足车辆收货");
        }
        // 采购收货列表
        List<VehicleReceivingDto> receivingList = request.getReceivingList();
        /*参数校验*/
        for (VehicleReceivingDto receivingDto : receivingList) {
            if (StringUtils.isBlank(receivingDto.getVin())) {
                return ResultResponse.businessFailed("车架号不能为空");
            }
            if (receivingDto.getVin().length() != 17) {
                return ResultResponse.businessFailed("车架号只能17位");
            }
            if (StringUtils.isBlank(receivingDto.getApplyDetailsNo())) {
                return ResultResponse.businessFailed("采购申请明细行号不能为空");
            }
        }
        // 遍历列表车架号去重 获取车架号列表
        List<String> vinList = receivingList.stream().map(VehicleReceivingDto::getVin).distinct().collect(Collectors.toList());
        if (vinList.size() != receivingList.size()) {
            return ResultResponse.businessFailed("收货车架号不能重复");
        }

        //采购车辆数量
        int purchaseQuantity = purchaseApply.getPurchaseQuantity();
        // 已收货车辆数
        int takeDeliveryQuantity = purchaseApply.getTakeDeliveryQuantity();
        if (purchaseQuantity < vinList.size() + takeDeliveryQuantity) {
            return ResultResponse.businessFailed("收货车辆数不能大于采购车辆数量");
        }

        // 判断车架号是否存在
        List<VehicleOrderReceipt> list = tableVehicleOrderReceiptService.queryListByVin(vinList);
        if (CollectionUtil.isNotEmpty(list)) {
            return ResultResponse.businessFailed(StrUtil.format("{}-收货车架号已存在", list.get(0).getVin()));
        }
        // 判断车架号是否存在
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        if (CollectionUtil.isNotEmpty(vehicleInfoList)) {
            return ResultResponse.businessFailed(StrUtil.format("{}-车架号已存在", vehicleInfoList.get(0).getVin()));
        }

        // 车型信息
        Map<Long, VehicleModelInfo> vehicleModelMap = tableVehicleModelInfoService.getAllVehicleModelMap();

        /*查询采购申请明细列表*/
        List<VehiclePurchaseApplyDetails> detailList = tablePurchaseApplyDetailsService.queryPurchaseDetailsList(purchaseApply.getId());
        if (CollectionUtil.isEmpty(detailList)) {
            return ResultResponse.businessFailed("采购申请明细为空");
        }
        Map<String, VehiclePurchaseApplyDetails> applyDetailsMap = detailList.stream().collect(Collectors.toMap(VehiclePurchaseApplyDetails::getApplyDetailsNo, details -> details));

        //receivingList 按照采购详情编号分组
        Map<String, List<VehicleReceivingDto>> receivingMap = receivingList.stream().collect(Collectors.groupingBy(VehicleReceivingDto::getApplyDetailsNo));

        // 遍历receivingMap
        for (Map.Entry<String, List<VehicleReceivingDto>> entry : receivingMap.entrySet()) {
            String applyDetailsNo = entry.getKey();
            // 采购明细信息
            VehiclePurchaseApplyDetails applyDetails = applyDetailsMap.get(applyDetailsNo);
            if (applyDetails == null) {
                throw new ServiceException(StrUtil.format("{}-采购申请明细不存在", applyDetailsNo));
            }
            // 收货车架号列表
            List<VehicleReceivingDto> receivingDtoList = entry.getValue();
            int quantity = applyDetails.getQuantity();
            int receivedQuantity = applyDetails.getReceivedQuantity();
            int allQuantity = receivedQuantity + receivingDtoList.size();
            if (quantity < allQuantity) {
                throw new ServiceException(StrUtil.format("采购申请明细{}-收货车辆数不能大于采购数量", applyDetailsNo));
            }
            // 保存收货信息
            for (VehicleReceivingDto dto : receivingDtoList) {
                VehicleOrderReceipt vehicleOrderReceipt = BeanUtil.copyProperties(dto, VehicleOrderReceipt.class);
                vehicleOrderReceipt.setVehicleModelId(dto.getVehicleModelId());
                vehicleOrderReceipt.setReceiptDate(dto.getReceiptDate());
                vehicleOrderReceipt.setPurchaseApplyId(purchaseApply.getId());
                vehicleOrderReceipt.setBusinessLine(applyDetails.getBusinessType());
                vehicleOrderReceipt.setApplyDetailsNo(applyDetailsNo);
                vehicleOrderReceipt.setOwnerId(dto.getOwnerId());
                tableVehicleOrderReceiptService.saveVehicleOrderReceipt(vehicleOrderReceipt, tokenUserInfo);

                // 生成车辆资产编号
                String assetNumber = redisUtils.generateUniqueId("ZC");
                VehicleInfo vehicleInfo = BeanUtil.copyProperties(vehicleOrderReceipt, VehicleInfo.class);
                vehicleInfo.setVehicleAssetId(assetNumber);
                vehicleInfo.setProductLine(purchaseApply.getApplyProductLine());
                vehicleInfo.setBusinessLine(applyDetails.getBusinessType());
                vehicleInfo.setAssetCompanyId(dto.getOwnerId());
                vehicleInfo.setSubscriptionCompanyCode(purchaseApply.getSubscriptionCompanyCode());
                vehicleInfo.setSubscriptionCompanyName(purchaseApply.getSubscriptionCompanyName());
                vehicleInfo.setObtainWayId(1);
                vehicleModelMap.get(dto.getVehicleModelId());
                if (dto.getVehicleModelId() != null && vehicleModelMap.get(dto.getVehicleModelId()) != null){
                    vehicleInfo.setEngineModel(vehicleModelMap.get(dto.getVehicleModelId()).getEngineModelNo());
                }
                tableVehicleService.saveVehicle(vehicleInfo, tokenUserInfo);

                VehicleManagementLegacyInfo managementLegacyInfo = new VehicleManagementLegacyInfo();
                managementLegacyInfo.setVin(dto.getVin());
                tableVehicleManagementLegacyService.insert(managementLegacyInfo, tokenUserInfo);

                // 添加车辆日志
                tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, "车辆收货", tokenUserInfo);
            }
            applyDetails.setReceivedQuantity(allQuantity);
            tablePurchaseApplyDetailsService.updatePurchaseApplyDetails(applyDetails);
        }

        purchaseApply.setTakeDeliveryQuantity(vinList.size() + takeDeliveryQuantity);
        tablePurchaseApplyService.updatePurchaseApply(purchaseApply, tokenUserInfo);

        tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "车辆采购申请单-操作单笔采购收货操作", tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse batchPurchasingReceiving(BaseImportFileUrlRequest request, TokenUserInfo tokenUserInfo) {
        List<ImportVehicleReceiving> readList = ExcelUtil.read(request.getFilePath(), 0, ImportVehicleReceiving.class);
        if (ObjectUtil.isEmpty(readList)) {
            throw new ServiceException("导入文件内容不能为空");
        }

        // 车架号去重
        List<String> vinList = readList.stream().map(ImportVehicleReceiving::getVin).distinct().collect(Collectors.toList());
        if (vinList.size() != readList.size()) {
            throw new ServiceException("收货车架号不能重复");
        }

        // 查询全部车型信息
        List<VehicleModelInfo> vehicleModelList = tableVehicleModelInfoService.getAllVehicleModel();
        Map<String, VehicleModelInfo> vehicleModelMap = vehicleModelList.stream().collect(Collectors.toMap(VehicleModelInfo::getVehicleModelName, vehicleModelInfo -> vehicleModelInfo));

        // 查询供应商列表
        DataDictResponse<Integer> supplierDict = dataDictService.queryDataMaintainDict(DataDictEnum.SUPPLIER.getValue());
        List<DataDictDto<Integer>> supplierDataDictList = supplierDict.getDataDictList();
        Map<String, DataDictDto> supplierDataDictMap = supplierDataDictList.stream().collect(Collectors.toMap(DataDictDto::getName, dataDictDto -> dataDictDto));

        // 车身颜色
        DataDictResponse<Integer> vehicleColorDict = dataDictService.queryDataMaintainDict(DataDictEnum.VEHICLE_COLOR.getValue());
        List<DataDictDto<Integer>> vehicleColorDataDictList = vehicleColorDict.getDataDictList();
        Map<String, DataDictDto> vehicleColorDataDictMap = vehicleColorDataDictList.stream().collect(Collectors.toMap(DataDictDto::getName, dataDictDto -> dataDictDto));

        // 车辆拥有公司
        DataDictResponse<Integer> ownerDict = dataDictService.queryDataMaintainDict(DataDictEnum.OWNER.getValue());
        List<DataDictDto<Integer>> dataDictList = ownerDict.getDataDictList();
        Map<String, DataDictDto<Integer>> ownerMapDataDictMap = dataDictList.stream().collect(Collectors.toMap(DataDictDto::getName, dataDictDto -> dataDictDto));

        Date now = DateTimeUtils.stringToDate(DateTimeUtils.dateToString(new Date(), DateTimeUtils.DATE_TYPE3), DateTimeUtils.DATE_TYPE3);
        for (ImportVehicleReceiving importVehicleReceiving : readList) {
            // 车型校验
            if (ObjectUtil.isEmpty(vehicleModelMap.get(importVehicleReceiving.getVehicleModelName()))) {
                throw new ServiceException(StrUtil.format("车架号{} {}-车型名称不存在", importVehicleReceiving.getVin(), importVehicleReceiving.getVehicleModelName()));
            }
            // 车辆拥有公司校验
            if (ObjectUtil.isEmpty(ownerMapDataDictMap.get(importVehicleReceiving.getAssetOwner()))) {
                throw new ServiceException(StrUtil.format("车架号{} {}-车辆资产所属公司不存在", importVehicleReceiving.getVin(), importVehicleReceiving.getAssetOwner()));
            }
            //是否回购校验
            PublicBooleanStatusEnum publicBooleanStatusEnum = PublicBooleanStatusEnum.getEnum(importVehicleReceiving.getIsRepurchase());
            if (ObjectUtil.isEmpty(publicBooleanStatusEnum)) {
                throw new ServiceException(StrUtil.format("车架号{} {}-是否回购参数错误", importVehicleReceiving.getVin(), importVehicleReceiving.getIsRepurchase()));
            }

            if (publicBooleanStatusEnum.getIsBoolean()) {
                if (ObjectUtil.isEmpty(importVehicleReceiving.getRepurchaseDate())) {
                    throw new ServiceException("回购时间不能为空");
                }
                if (ObjectUtil.isEmpty(importVehicleReceiving.getRepurchaseRequirements())) {
                    throw new ServiceException("回购要求不能为空");
                }
            }
            // 校验供应商 根据供应商名称查询ID
            if (ObjectUtil.isEmpty(supplierDataDictMap.get(importVehicleReceiving.getSupplier()))) {
                return ResultResponse.businessFailed(StrUtil.format("车架号{} {}-供应商不存在", importVehicleReceiving.getVin(), importVehicleReceiving.getSupplier()));
            }
            // 车身颜色校验
            if (ObjectUtil.isEmpty(vehicleColorDataDictMap.get(importVehicleReceiving.getBodyColor()))) {
                return ResultResponse.businessFailed(StrUtil.format("车架号{} {}-车身颜色不存在", importVehicleReceiving.getVin(), importVehicleReceiving.getBodyColor()));
            }
            // 下单日期及收货日期必须为今天及今天之前的日期
            Date orderDate = DateTimeUtils.stringToDate(importVehicleReceiving.getOrderDate(), DateTimeUtils.DATE_TYPE3);
            Date receiptDate = DateTimeUtils.stringToDate(importVehicleReceiving.getReceiptDate(), DateTimeUtils.DATE_TYPE3);
            if (orderDate.after(now) || receiptDate.after(now)) {
                return ResultResponse.businessFailed(StrUtil.format("车架号{} {}-下单日期或收货日期不能大于今天", importVehicleReceiving.getVin(), importVehicleReceiving.getVin()));
            }
        }

        // 判断收货信息是否包含重复车架号
        List<VehicleOrderReceipt> list = tableVehicleOrderReceiptService.queryListByVin(vinList);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new ServiceException(StrUtil.format("{}-收货车架号已存在", list.get(0).getVin()));
        }
        // 判断车辆是否存在
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        if (CollectionUtil.isNotEmpty(vehicleInfoList)) {
            throw new ServiceException(StrUtil.format("{}-车架号已存在", vehicleInfoList.get(0).getVin()));
        }

        // 按照approvalNumber 采购申请钉钉号字段分组
        Map<String, List<ImportVehicleReceiving>> map = readList.stream().collect(Collectors.groupingBy(ImportVehicleReceiving::getApprovalNumber));
        for (Map.Entry<String, List<ImportVehicleReceiving>> entry : map.entrySet()) {
            // 采购申请钉钉号
            String approvalNumber = entry.getKey();
            // 查询采购申请信息
            VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApplyApprovalNumber(approvalNumber);
            if (purchaseApply == null) {
                throw new ServiceException(StrUtil.format("{}-采购申请信息不存在", approvalNumber));
            }
            // 采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
            int purchaseApplyStatus = purchaseApply.getPurchaseApplyStatus();
            if (purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_2
                    && purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_3) {
                throw new ServiceException(StrUtil.format("{}-采购申请状态异常，不满足车辆收货", approvalNumber));
            }

            // 采购收货明细
            List<ImportVehicleReceiving> receivingList = entry.getValue();
            // 采购车辆数
            int purchaseQuantity = purchaseApply.getPurchaseQuantity();
            // 已收货车辆数
            int takeDeliveryQuantity = purchaseApply.getTakeDeliveryQuantity();
            // 收货车辆数
            int receiveQuantity = receivingList.size();
            if (takeDeliveryQuantity + receiveQuantity > purchaseQuantity) {
                throw new ServiceException(StrUtil.format("{}-采购车辆收货数超出采购申请数量", approvalNumber));
            }

            /*查询采购申请单明细列表*/
            List<VehiclePurchaseApplyDetails> detailList = tablePurchaseApplyDetailsService.queryPurchaseDetailsList(purchaseApply.getId());
            if (CollectionUtil.isEmpty(detailList)) {
                throw new ServiceException(StrUtil.format("{}-采购明细信息不存在", approvalNumber));
            }
            Map<String, VehiclePurchaseApplyDetails> applyDetailsMap = detailList.stream().collect(Collectors.toMap(VehiclePurchaseApplyDetails::getApplyDetailsNo, details -> details));

            //receivingList 按照采购申请明细编号分组
            Map<String, List<ImportVehicleReceiving>> receivingMap = receivingList.stream().collect(Collectors.groupingBy(ImportVehicleReceiving::getApplyDetailsNo));
            for (Map.Entry<String, List<ImportVehicleReceiving>> receivingEntry : receivingMap.entrySet()) {
                String applyDetailsNo = receivingEntry.getKey();
                // 采购明细信息
                VehiclePurchaseApplyDetails applyDetails = applyDetailsMap.get(applyDetailsNo);
                if (applyDetails == null) {
                    throw new ServiceException(StrUtil.format("{}-采购申请明细不存在", applyDetailsNo));
                }
                // 收货车架号列表
                List<ImportVehicleReceiving> vehicleReceivingList = receivingEntry.getValue();
                int quantity = applyDetails.getQuantity();
                int receivedQuantity = applyDetails.getReceivedQuantity();
                if (quantity < receivedQuantity + vehicleReceivingList.size()) {
                    throw new ServiceException(StrUtil.format("采购申请明细{}-收货车辆数不能大于采购数量", applyDetailsNo));
                }

                for (ImportVehicleReceiving receiving : vehicleReceivingList) {
                    VehicleOrderReceipt vehicleOrderReceipt = new VehicleOrderReceipt();
                    vehicleOrderReceipt.setPurchaseApplyId(purchaseApply.getId());
                    vehicleOrderReceipt.setApplyDetailsNo(applyDetailsNo);
                    vehicleOrderReceipt.setVin(receiving.getVin());
                    vehicleOrderReceipt.setEngineNo(receiving.getEngineNo());
                    vehicleOrderReceipt.setInteriorColor(receiving.getInteriorColor());
                    // 车型
                    VehicleModelInfo vehicleModelInfo = vehicleModelMap.get(receiving.getVehicleModelName());
                    vehicleOrderReceipt.setVehicleModelId(vehicleModelInfo.getId());
                    // 车身颜色
                    DataDictDto<Integer> vehicleColorDataDict = vehicleColorDataDictMap.get(receiving.getBodyColor());
                    vehicleOrderReceipt.setVehicleColorId(vehicleColorDataDict.getValue());
                    // 供应商
                    DataDictDto<Integer> supplierDataDict = supplierDataDictMap.get(receiving.getSupplier());
                    vehicleOrderReceipt.setSupplierId(supplierDataDict.getValue());
                    // 是否回购
                    PublicBooleanStatusEnum publicBooleanStatusEnum = PublicBooleanStatusEnum.getEnum(receiving.getIsRepurchase());
                    vehicleOrderReceipt.setIsRepurchase(publicBooleanStatusEnum.getCode());
                    vehicleOrderReceipt.setRepurchaseRequirements(receiving.getRepurchaseRequirements());
                    vehicleOrderReceipt.setRepurchaseDate(DateTimeUtils.stringToDate(receiving.getRepurchaseDate(), DateTimeUtils.DATE_TYPE3));

                    vehicleOrderReceipt.setOrderDate(DateTimeUtils.stringToDate(receiving.getOrderDate(), DateTimeUtils.DATE_TYPE3));
                    vehicleOrderReceipt.setReceiptDate(DateTimeUtils.stringToDate(receiving.getReceiptDate(), DateTimeUtils.DATE_TYPE3));
                    vehicleOrderReceipt.setBusinessLine(applyDetails.getBusinessType());
                    // 车辆拥有公司
                    DataDictDto<Integer> ownerDataDict = ownerMapDataDictMap.get(receiving.getAssetOwner());
                    vehicleOrderReceipt.setOwnerId(ownerDataDict.getValue());
                    tableVehicleOrderReceiptService.saveVehicleOrderReceipt(vehicleOrderReceipt, tokenUserInfo);

                    VehicleInfo vehicleInfo = BeanUtil.copyProperties(vehicleOrderReceipt, VehicleInfo.class);
                    // 生成车辆资产编号
                    String assetNumber = redisUtils.generateUniqueId("ZC");
                    vehicleInfo.setVehicleAssetId(assetNumber);
                    vehicleInfo.setAssetCompanyId(ownerDataDict.getValue());
                    vehicleInfo.setVehicleModelId(vehicleModelInfo.getId());
                    vehicleInfo.setEngineModel(vehicleModelInfo.getEngineModelNo());
                    vehicleInfo.setProductLine(purchaseApply.getApplyProductLine());
                    vehicleInfo.setBusinessLine(applyDetails.getBusinessType());
                    vehicleInfo.setSubscriptionCompanyCode(purchaseApply.getSubscriptionCompanyCode());
                    vehicleInfo.setSubscriptionCompanyName(purchaseApply.getSubscriptionCompanyName());
                    vehicleInfo.setObtainWayId(1);
                    tableVehicleService.saveVehicle(vehicleInfo, tokenUserInfo);

                    VehicleManagementLegacyInfo managementLegacyInfo = new VehicleManagementLegacyInfo();
                    managementLegacyInfo.setVin(receiving.getVin());
                    tableVehicleManagementLegacyService.insert(managementLegacyInfo, tokenUserInfo);
                    // 添加车辆日志
                    tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, "车辆批量收货", tokenUserInfo);
                }
                applyDetails.setReceivedQuantity(receivedQuantity + vehicleReceivingList.size());
                tablePurchaseApplyDetailsService.updatePurchaseApplyDetails(applyDetails);
            }
            purchaseApply.setTakeDeliveryQuantity(takeDeliveryQuantity + receiveQuantity);
            tablePurchaseApplyService.updatePurchaseApply(purchaseApply, tokenUserInfo);

            tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "车辆采购申请单-操作批量采购收货操作", tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseQuotaDto updatePurchaseQuota(String vin, TokenUserInfo tokenUserInfo) {
        // 查询采购收货单详情
        VehicleOrderReceipt vehicleOrderReceipt = tableVehicleOrderReceiptService.queryVehicleOrderReceipt(vin);
        if (vehicleOrderReceipt == null || StringUtils.isBlank(vehicleOrderReceipt.getApplyDetailsNo())) {
            return null;
        }
        // 车辆上牌标记 1-是 2-否
        int vehicleRegistrationMark = vehicleOrderReceipt.getVehicleRegistrationMark();
        if (vehicleRegistrationMark == 1) {
            return null;
        }
        // 查询车辆采购申请明细
        VehiclePurchaseApplyDetails applyDetails = tablePurchaseApplyDetailsService.queryPurchaseApplyDetails(vehicleOrderReceipt.getApplyDetailsNo());
        if (applyDetails == null || applyDetails.getIsQuotaOccupied() == null || applyDetails.getIsQuotaOccupied() != 1) {
            return null;
        }
        // 查询采购申请任务
        VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.selectById(applyDetails.getPurchaseApplyId());
        if (purchaseApply == null) {
            return null;
        }
        // 返回额度调整结果
        PurchaseQuotaDto purchaseQuotaDto = new PurchaseQuotaDto();
        purchaseQuotaDto.setQuotaType(applyDetails.getQuotaType());
        purchaseQuotaDto.setQuotaAssetOwnership(applyDetails.getQuotaAssetOwnership());
        purchaseQuotaDto.setIsChangePreOccupied(false);

        // 修改采购详情预占用额度数
        if (applyDetails.getPreOccupied() > 0) {
            // 更新更新额度一览
            UpdateQuotaDto updateQuotaDTO = getUpdateQuotaDto(applyDetails);
            licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo,true);
            purchaseQuotaDto.setIsChangePreOccupied(true);
            applyDetails.setPreOccupied(applyDetails.getPreOccupied() - 1);
            tablePurchaseApplyDetailsService.updatePurchaseApplyDetails(applyDetails);
            // 如果审批单详情预占用额度数大于0，则修改采购单预占用额度数
            purchaseApply.setPreOccupied(purchaseApply.getPreOccupied() - 1);
        }
        // 更新车辆上牌标记
        vehicleOrderReceipt.setVehicleRegistrationMark(1);
        tableVehicleOrderReceiptService.updateSelectiveById(vehicleOrderReceipt, tokenUserInfo);
        // 修改采购单上牌额度数
        purchaseApply.setVehicleOccupied(purchaseApply.getVehicleOccupied() + 1);
        tablePurchaseApplyService.updatePurchaseApply(purchaseApply, tokenUserInfo);
        tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "车辆采购申请单-车辆上牌，车牌占用额度增加", tokenUserInfo);
        return purchaseQuotaDto;
    }

    @Override
    public ResultResponse refreshPurchasing(RefreshDingTalkApprovalRequest request, TokenUserInfo tokenUserInfo) {
        VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApplyApprovalNumber(request.getApprovalNumber());
        if (purchaseApply == null) {
            return ResultResponse.success();
        }
        // 采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
        int purchaseApplyStatus = purchaseApply.getPurchaseApplyStatus();
        if (purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_2) {
            return ResultResponse.success();
        }
        GetDingTalkWorkFlowRequest queryFormInstanceRequest = new GetDingTalkWorkFlowRequest();
        queryFormInstanceRequest.setInstanceId(purchaseApply.getApprovalNumber());
        GetDingTalkDetailResponse getDingTalkDetailResponse = dingTalkService.getDingTalkDetailFlow(queryFormInstanceRequest);
        String workFlowResult = getDingTalkDetailResponse.getWorkFlowResult();
        String result = DingTalkConstant.dingTalkResultMap.get(workFlowResult);
        if (StringUtils.isBlank(result)) {
            return ResultResponse.success();
        }
        // 调用审批结果逻辑
        dingTalkResultProcess(purchaseApply.getApprovalNumber(), result);
        return ResultResponse.success();
    }

    private static UpdateQuotaDto getUpdateQuotaDto(VehiclePurchaseApplyDetails applyDetails) {
        //额度类型枚举值  1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营
        int quotaType = applyDetails.getQuotaType();
        // 额度资产归属公司
        int quotaAssetOwnership = applyDetails.getQuotaAssetOwnership();
        UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
        updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.PRE_OCCUPY.getCode());
        updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.DECREASE.getCode());
        updateQuotaDTO.setAdjustNum(1);
        updateQuotaDTO.setQuotaType(quotaType);
        updateQuotaDTO.setAssetCompanyId(quotaAssetOwnership);
        return updateQuotaDTO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dingTalkResultProcess(String requestNo, String dingTalkResult) {
        VehiclePurchaseApply purchaseApply = tablePurchaseApplyService.queryPurchaseApplyApprovalNumber(requestNo);
        if (purchaseApply == null) {
            log.error("采购申请信息不存在，requestNo:{}", requestNo);
            return;
        }
        //采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
        int purchaseApplyStatus = purchaseApply.getPurchaseApplyStatus();
        if (purchaseApplyStatus != BizConstant.ApplyStatus.applyStatus_2) {
            return;
        }

        GetDingTalkWorkFlowRequest queryFormInstanceRequest = new GetDingTalkWorkFlowRequest();
        queryFormInstanceRequest.setInstanceId(purchaseApply.getApprovalNumber());
        GetDingTalkDetailResponse getDingTalkDetailResponse = dingTalkService.getDingTalkDetailFlow(queryFormInstanceRequest);
        List<RowDto> rowDtoList = getDingTalkDetailResponse.getPurchaseDetailData();
        Map<String,PurchaseApplyDetailDto> purchaseApplyDetailMap = new HashMap<>();
        for (RowDto rowDto : rowDtoList) {
            PurchaseApplyDetailDto vehicleDisposalDetailDto = rowDto.convertToPurchaseApplyDetailDto();
            purchaseApplyDetailMap.put(vehicleDisposalDetailDto.getApplyDetailsNo(),vehicleDisposalDetailDto);
        }

        // 查询采购申请明细
        List<VehiclePurchaseApplyDetails> detailList = tablePurchaseApplyDetailsService.queryPurchaseDetailsList(purchaseApply.getId());

        TokenUserInfo tokenUserInfo = new TokenUserInfo();
        tokenUserInfo.setUserId(-1L);
        tokenUserInfo.setName("钉钉审批回调");
        if (dingTalkResult.equals("agree")) {
            for (VehiclePurchaseApplyDetails applyDetails : detailList) {
                PurchaseApplyDetailDto applyDetailDto = purchaseApplyDetailMap.get(applyDetails.getApplyDetailsNo());
                if (applyDetailDto != null) {
                    // 投资回报率
                    applyDetails.setReturnOnInvestment(applyDetailDto.getReturnOnInvestment());
                    applyDetails.setMonthlyBicycleRevenue(applyDetailDto.getMonthlyBicycleRevenue());
                    applyDetails.setVehicleResidualValueRate(applyDetailDto.getVehicleResidualValueRate());
                }
                tablePurchaseApplyDetailsService.updatePurchaseApplyDetails(applyDetails);
            }
            purchaseApply.setPurchaseApplyStatus(BizConstant.ApplyStatus.applyStatus_3);
            tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "审批车辆采购申请单结果-审批通过", tokenUserInfo);
        } else if (dingTalkResult.equals("refuse")) {
            // 审批拒绝 自动释放占用额度 调用占用上牌额度服务-归还上牌额度
            for (VehiclePurchaseApplyDetails applyDetails : detailList) {
                //是否占用管控额度 1-是 2-否
                int isQuotaOccupied = applyDetails.getIsQuotaOccupied() == null ? 2 : applyDetails.getIsQuotaOccupied();
                int preOccupied = applyDetails.getPreOccupied() == null ? applyDetails.getQuantity() : applyDetails.getPreOccupied();
                if (isQuotaOccupied == 1 || preOccupied > 0) {
                    UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
                    updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.PRE_OCCUPY.getCode());
                    updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.DECREASE.getCode());
                    updateQuotaDTO.setAdjustNum(preOccupied);
                    updateQuotaDTO.setQuotaType(applyDetails.getQuotaType());
                    updateQuotaDTO.setAssetCompanyId(applyDetails.getQuotaAssetOwnership());
                    licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo, true);
                }
                applyDetails.setPreOccupied(0);
                PurchaseApplyDetailDto applyDetailDto = purchaseApplyDetailMap.get(applyDetails.getApplyDetailsNo());
                if (applyDetailDto != null){
                    // 投资回报率
                    applyDetails.setReturnOnInvestment(applyDetailDto.getReturnOnInvestment());
                    applyDetails.setMonthlyBicycleRevenue(applyDetailDto.getMonthlyBicycleRevenue());
                    applyDetails.setVehicleResidualValueRate(applyDetailDto.getVehicleResidualValueRate());
                }
                tablePurchaseApplyDetailsService.updatePurchaseApplyDetails(applyDetails);
            }
            purchaseApply.setPreOccupied(0);
            purchaseApply.setPurchaseApplyStatus(BizConstant.ApplyStatus.applyStatus_4);
            tableOperateLogService.insertLog(purchaseApply.getId(), BizConstant.BusinessType.businessType_3, null, "审批车辆采购申请单结果-审批拒绝", tokenUserInfo);
        }
        tablePurchaseApplyService.updatePurchaseApply(purchaseApply, tokenUserInfo);
    }

    /**
     * 构建审批参数
     *
     * @param purchaseApply
     * @return
     */
    private CreateDingTalkWorkFlowRequest getDingTalkWorkFlow(VehiclePurchaseApply purchaseApply,List<PurchaseApplyDetailDto> applyDetailList) {
        // 统计applyDetailList 预算内的总价
        BigDecimal budgetInTotalPrice = BigDecimal.ZERO;
        // 统计applyDetailList 预算外的总价
        BigDecimal budgetOutTotalPrice = BigDecimal.ZERO;
        //采购产品线 1-寻网业务 2-商务业务
        int applyProductLine = purchaseApply.getApplyProductLine();
        for (PurchaseApplyDetailDto dto : applyDetailList) {
            if (applyProductLine == 1) {
                // 预算情况 1-预算内 2-预算外
                Integer budgetStatus = dto.getBudgetStatus();
                if (budgetStatus != null && budgetStatus == 1) {
                    budgetInTotalPrice = budgetInTotalPrice.add(dto.getTotalPrice());
                }
                if (budgetStatus != null && budgetStatus == 2) {
                    budgetOutTotalPrice = budgetOutTotalPrice.add(dto.getTotalPrice());
                }
            }
            // 校验商务业务采购车型信息
            Long purchaseModelId = dto.getPurchaseModelId();
            VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(purchaseModelId);
            if (vehicleModelInfo == null) {
                throw new RuntimeException("未查询到采购车型信息");
            }
            //车身-宽度（mm）
            int outWidth = vehicleModelInfo.getOutWidth() == null ? 0 : vehicleModelInfo.getOutWidth();
            //车身-高度（mm）
            int outHeight = vehicleModelInfo.getOutHeight() == null ? 0 : vehicleModelInfo.getOutHeight();
            //车身-长度（mm）
            int outLength = vehicleModelInfo.getOutLength() == null ? 0 : vehicleModelInfo.getOutLength();
            dto.setVehicleSize(StrUtil.format("{}*{}*{}", outLength, outWidth, outHeight));
        }
        for (PurchaseApplyDetailDto dto : applyDetailList) {
            // 校验商务业务采购车型信息
            Long purchaseModelId = dto.getPurchaseModelId();
            VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(purchaseModelId);
            if (vehicleModelInfo == null) {
                throw new RuntimeException("未查询到采购车型信息");
            }
            //车身-宽度（mm）
            int outWidth = vehicleModelInfo.getOutWidth() == null ? 0 : vehicleModelInfo.getOutWidth();
            //车身-高度（mm）
            int outHeight = vehicleModelInfo.getOutHeight() == null ? 0 : vehicleModelInfo.getOutHeight();
            //车身-长度（mm）
            int outLength = vehicleModelInfo.getOutLength() == null ? 0 : vehicleModelInfo.getOutLength();
            dto.setVehicleSize(StrUtil.format("{}*{}*{}", outLength, outWidth, outHeight));
        }
        //查询字典表相关信息
        List<String> systemCodeList = Arrays.asList(
                //供应商
                DataDictEnum.SUPPLIER.getValue(),
                //供应商大类
                DataDictEnum.SUPPLIER_TYPE.getValue(),
                //制造商
                DataDictEnum.MANUFACTURER.getValue(),
                //组织机构
                DataDictEnum.OWNER.getValue()
        );
        Map<String, Map<Integer, String>> dataMaintainDictMap = dataDictService.getDataMaintainDictMap(systemCodeList);
        // 查询供应商列表
        Map<Integer, String> supplierMap = dataMaintainDictMap.get(DataDictEnum.SUPPLIER.getValue());
        // 查询制造商
        Map<Integer, String> manufacturerMap = dataMaintainDictMap.get(DataDictEnum.MANUFACTURER.getValue());
        // 车辆拥有公司
        Map<Integer, String> ownerMap = dataMaintainDictMap.get(DataDictEnum.OWNER.getValue());
        // 车型信息
        Map<Long, VehicleModelInfo> vehicleModelMap = tableVehicleModelInfoService.getAllVehicleModelMap();
        // 供应商大类
        Map<Integer, String> supplierTypeMap = dataMaintainDictMap.get(DataDictEnum.SUPPLIER_TYPE.getValue());
        OrgInfo orgInfo = tableOrgInfoService.queryOrgInfoById(purchaseApply.getApplyOrgId());
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("单据标题", purchaseApply.getApplyName());
        paramMap.put("单据所属资产公司", ObjectValidUtil.formatStr(ownerMap.get(purchaseApply.getOwnerId())));
        paramMap.put("单据所属机构", orgInfo == null ? "" : orgInfo.getCompanyName());
        paramMap.put("申购公司", purchaseApply.getSubscriptionCompanyCode());
        paramMap.put("条线", ProductLineEnum.getDesc(purchaseApply.getApplyProductLine()));
        paramMap.put("采购车辆数", String.valueOf(purchaseApply.getPurchaseQuantity()));
        paramMap.put("申购理由", purchaseApply.getApplyRemark());
        paramMap.put("巡网预算内产品总价（元）", String.valueOf(budgetInTotalPrice));
        paramMap.put("巡网预算外产品总价（元）", String.valueOf(budgetOutTotalPrice));
        paramMap.put("产品总价（元）（商务线）", "0");
        paramMap.put("供应商大类", ObjectValidUtil.formatStr(supplierTypeMap.get(purchaseApply.getSupplierType())));

        // 查询意向
        VehiclePurchaseIntention intention = tablePurchaseIntentionService.queryPurchaseIntention(purchaseApply.getIntentionNo());
        if (intention != null) {
            List<List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails>> result = new ArrayList<>();
            List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails> detailsList = new ArrayList<>();
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("合同编号").setValue(ObjectValidUtil.formatStr(intention.getContractNo())));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("客户信息").setValue(ObjectValidUtil.formatStr(intention.getCustomerUser())));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车型名").setValue(ObjectValidUtil.formatStr(intention.getVehicleModelName())));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车型ID").setValue(ObjectValidUtil.formatStr((intention.getVehicleModelId()))));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("数量（台）").setValue(ObjectValidUtil.formatStr(intention.getQuantity())));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("指导价（元）").setValue(ObjectValidUtil.formatStr(intention.getGuidePrice())));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车身颜色").setValue(ObjectValidUtil.formatStr(intention.getVehicleBodyColor())));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("内饰颜色").setValue(ObjectValidUtil.formatStr(intention.getVehicleInteriorColor())));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("客户期望到车日期").setValue(ObjectValidUtil.formatStr(DateTimeUtils.dateToString(intention.getExpectedArrivalDate(), DateTimeUtils.DATE_TYPE3))));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("约定最晚到车日期").setValue(ObjectValidUtil.formatStr(DateTimeUtils.dateToString(intention.getExpectedArrivalLastDate(), DateTimeUtils.DATE_TYPE3))));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("合同约定牌照属性").setValue(ObjectValidUtil.formatStr(intention.getLicensePlateAttribute())));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("合同约定牌照所属地").setValue(ObjectValidUtil.formatStr(intention.getLicensePlateLocation())));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("装潢需求").setValue(ObjectValidUtil.formatStr(intention.getDecorationDemand())));
            detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("销售姓名").setValue(ObjectValidUtil.formatStr(intention.getSaleName())));
            result.add(detailsList);
            paramMap.put("采购意向", JSON.toJSONString(result));
        }
        // 查询采购详情
        if (CollectionUtil.isNotEmpty(applyDetailList)) {
            List<List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails>> result = new ArrayList<>();
            applyDetailList.forEach(details -> {
                List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails> detailsList = new ArrayList<>();
                VehicleModelInfo vehicleModelInfo = vehicleModelMap.get(details.getPurchaseModelId());
                String manufacturerName = manufacturerMap.get(details.getManufacturerId());
                String supplier = supplierMap.get(details.getSupplierId());
                String quotaAssetOwnershipName = ownerMap.get(details.getQuotaAssetOwnership());
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("申请明细行号").setValue(ObjectValidUtil.formatStr(details.getApplyDetailsNo())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("预算情况（巡网）").setValue(BudgetStatusEnum.getDesc(details.getBudgetStatus())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("采购类型").setValue(PurchaseTypeEnum.getDesc(details.getPurchaseType())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("采购内容（车型）").setValue(ObjectValidUtil.formatStr(vehicleModelInfo.getVehicleModelName())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("单价（元）（巡网）").setValue(ObjectValidUtil.formatStr((details.getUnitPrice()))));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("数量").setValue(ObjectValidUtil.formatStr(details.getQuantity())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("其他费用（元）（巡网）").setValue(ObjectValidUtil.formatStr(details.getOtherCosts())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("总价（元）（巡网）").setValue(ObjectValidUtil.formatStr(details.getTotalPrice())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("业务类型").setValue(BusinessLineEnum.getDesc(details.getBusinessType())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("制造商").setValue(ObjectValidUtil.formatStr(manufacturerName)));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("供应商").setValue(ObjectValidUtil.formatStr(supplier)));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("已使用年限（二手车）").setValue(ObjectValidUtil.formatStr(details.getUsedYears())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("已使用公里数（二手车）").setValue(ObjectValidUtil.formatStr(details.getUsedKm())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车辆预计退运时间（商务）").setValue(ObjectValidUtil.formatStr(DateTimeUtils.dateToString(details.getExpectedReturnDate(), DateTimeUtils.DATE_TYPE3))));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("期望到货日期").setValue(ObjectValidUtil.formatStr(DateTimeUtils.dateToString(details.getExpectedDeliveryDate(), DateTimeUtils.DATE_TYPE3))));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("是否占用管控额度").setValue(PublicBooleanStatusEnum.getDesc(details.getIsQuotaOccupied())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("额度类型").setValue(QuotaTypeEnum.getDesc(details.getQuotaType())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("额度资产归属公司").setValue(ObjectValidUtil.formatStr(quotaAssetOwnershipName)));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("长*宽*高").setValue(ObjectValidUtil.formatStr(details.getVehicleSize())));
                result.add(detailsList);
            });
            paramMap.put("采购详情", JSON.toJSONString(result));
        }
        // 查询采购设备
        List<VehiclePurchaseEquipment> equipmentList = tablePurchaseEquipmentService.queryPurchaseEquipmentList(purchaseApply.getId());
        if (CollectionUtil.isNotEmpty(equipmentList)) {
            List<List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails>> result = new ArrayList<>();
            equipmentList.forEach(equipment -> {
                List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails> detailsList = new ArrayList<>();
                String manufacturerName = manufacturerMap.get(equipment.getManufacturerId());
                String supplier = supplierMap.get(equipment.getSupplierId());
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("设备名称").setValue(ObjectValidUtil.formatStr(equipment.getEquipmentName())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("制造商").setValue(ObjectValidUtil.formatStr(manufacturerName)));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("供应商").setValue(ObjectValidUtil.formatStr(supplier)));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("单价（元）").setValue(ObjectValidUtil.formatStr(equipment.getUnitPrice())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("数量").setValue(ObjectValidUtil.formatStr(equipment.getQuantity())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("其他费用（元）").setValue(ObjectValidUtil.formatStr(equipment.getOtherCosts())));
                detailsList.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("设备总价（元）").setValue(ObjectValidUtil.formatStr(equipment.getTotalPrice())));
                result.add(detailsList);
            });
            paramMap.put("车载设备（巡网）", JSON.toJSONString(result));
        }
        CreateDingTalkWorkFlowRequest createDingTalkWorkFlowRequest = new CreateDingTalkWorkFlowRequest();
        createDingTalkWorkFlowRequest.setProcessCode(dingTalkConfig.getVehiclePurchaseProcessCode());
        createDingTalkWorkFlowRequest.setProcessComponentValues(paramMap);
        return createDingTalkWorkFlowRequest;
    }
}
