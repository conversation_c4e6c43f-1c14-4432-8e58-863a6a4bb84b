package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.dazhong.transportation.vlms.config.DingTalkConfig;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.database.*;
import com.dazhong.transportation.vlms.dto.*;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleApplicationDetailResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleListResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.*;
import com.dazhong.transportation.vlms.excel.*;
import com.dazhong.transportation.vlms.excel.util.ExcelUtil;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.*;
import com.dazhong.transportation.vlms.service.IDingTalkService;
import com.dazhong.transportation.vlms.service.IVehicleApplicationService;
import com.dazhong.transportation.vlms.service.IVehicleService;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VehicleApplicationServiceImpl implements IVehicleApplicationService {

    @Autowired
    private TableVehicleApplicationService tableVehicleApplicationService;

    @Autowired
    private TableVehicleApplicationDetailService tableVehicleApplicationDetailService;

    @Autowired
    private TableApplyFileService tableApplyFileService;

    @Autowired
    private TableVehicleService tableVehicleService;

    @Autowired
    private TableVehicleModelInfoService tableVehicleModelInfoService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Autowired
    private TableDataOwnerInfoService tableDataOwnerInfoService;

    @Autowired
    private IDingTalkService dingTalkService;

    @Autowired
    private DingTalkConfig dingTalkConfig;

    @Autowired
    private IVehicleService vehicleService;

    @Override
    public PageResponse<VehicleApplicationListDto> queryVehicleApplicationPageResponse(SearchVehicleApplicationListRequest request, TokenUserInfo tokenUserInfo) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehicleApplicationListDto> vehicleApplicationList = tableVehicleApplicationService.queryVehicleApplicationList(request, tokenUserInfo);
        PageInfo<VehicleApplicationListDto> pageInfo = new PageInfo<>(vehicleApplicationList);

        return new PageResponse<>(pageInfo.getTotal(), vehicleApplicationList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse<Long> saveApplication(SaveVehicleApplicationRequest request, TokenUserInfo tokenUserInfo) {
        // 1、保存车辆主数据申请单
        VehicleApplication vehicleApplication = BeanUtil.copyProperties(request, VehicleApplication.class);
        if (null == request.getId()) {
            tableVehicleApplicationService.insert(vehicleApplication, tokenUserInfo);
        } else {
            tableVehicleApplicationService.updateSelectiveById(vehicleApplication, tokenUserInfo);
        }

        // 2、保存车辆主数据申请单明细
        if (CollectionUtil.isNotEmpty(request.getVehicleApplicationDetailList())) {
            List<String> vinList = request.getVehicleApplicationDetailList().stream().map(VehicleApplicationDetailDto::getVin).distinct().collect(Collectors.toList());
            if (vinList.size() != request.getVehicleApplicationDetailList().size()) {
                throw new ServiceException("明细数据异常，重复车架号！");
            }

            tableVehicleApplicationDetailService.deleteByApplicationId(vehicleApplication.getId());
            List<VehicleApplicationDetail> vehicleApplicationDetailList = new ArrayList<>();
            for (VehicleApplicationDetailDto vehicleApplicationDetailDto : request.getVehicleApplicationDetailList()) {
                if (!Objects.equals(vehicleApplicationDetailDto.getProductLine(), request.getProductLine())) {
                    throw new ServiceException("【" + vehicleApplicationDetailDto.getVin() + "】车架号，业务线与表单不一致！");
                }
                vehicleApplicationDetailDto.setVehicleModelId(vehicleApplicationDetailDto.getVehicleModelId());
                vehicleApplicationDetailDto.setVehicleModelName(vehicleApplicationDetailDto.getVehicleModelName());
                VehicleApplicationDetail vehicleApplicationDetail = BeanUtil.copyProperties(vehicleApplicationDetailDto, VehicleApplicationDetail.class);
                vehicleApplicationDetail.setApplicationId(vehicleApplication.getId());
                vehicleApplicationDetailList.add(vehicleApplicationDetail);
            }
            tableVehicleApplicationDetailService.batchInsert(vehicleApplicationDetailList, tokenUserInfo);
        }

        // 3、保存添加附件
        if (CollectionUtil.isNotEmpty(request.getVehicleApplyFileList())) {
            tableApplyFileService.deleteApplyFile(vehicleApplication.getId(), FileBusinessTypeEnum.VEHICLE_MASTER_DATA_APPLICATION.getCode());
            for (VehicleApplyFileDto vehicleApplyFileDto : request.getVehicleApplyFileList()) {
                VehicleApplyFile vehicleApplyFile = new VehicleApplyFile();
                BeanUtil.copyProperties(vehicleApplyFileDto, vehicleApplyFile);
                if (vehicleApplyFile.getFileUrl().contains(Global.instance.mfsUrl)) {
                    vehicleApplyFile.setFileUrl(vehicleApplyFile.getFileUrl().replace(Global.instance.mfsUrl + "/", ""));
                }
                vehicleApplyFile.setForeignId(vehicleApplication.getId());
                vehicleApplyFile.setBusinessType(FileBusinessTypeEnum.VEHICLE_MASTER_DATA_APPLICATION.getCode());
                tableApplyFileService.insert(vehicleApplyFile, tokenUserInfo);
            }
        }

        return ResultResponse.success(vehicleApplication.getId());
    }

    @Override
    public ResultResponse<Void> submitApplication(SaveVehicleApplicationRequest request, TokenUserInfo tokenUserInfo) {
        // 保存处置申请单
        request.setCreateOperName(tokenUserInfo.getName());
        ResultResponse<Long> longResultResponse = saveApplication(request, tokenUserInfo);
        if (null == longResultResponse.getData() || longResultResponse.getData() <= 0) {
            throw new ServiceException("保存车辆申请单失败");
        }
        // 提交钉钉审批流
        CreateDingTalkWorkFlowRequest createDingTalkWorkFlowRequest = new CreateDingTalkWorkFlowRequest();
        if (request.getApplicationType() == 1) {
            createDingTalkWorkFlowRequest.setProcessCode(dingTalkConfig.getVehicleAllocateProcessCode());
        }
        if (request.getApplicationType() == 2) {
            createDingTalkWorkFlowRequest.setProcessCode(dingTalkConfig.getVehicleTransferProcessCode());
        }
        if (request.getApplicationType() == 3) {
            createDingTalkWorkFlowRequest.setProcessCode(dingTalkConfig.getVehicleModifyBusinessProcessCode());
        }
        createDingTalkWorkFlowRequest.setProcessComponentValues(request.convertToDingTalkData());
        createDingTalkWorkFlowRequest.setOriginatorUserId(tokenUserInfo.getDingTalkNum());
        createDingTalkWorkFlowRequest.setOriginatorDeptId(request.getOriginatorDeptId());
        if (CollectionUtil.isNotEmpty(request.getVehicleApplyFileList())) {
            CommitDingFlowAttachmentDto attachmentDto = new CommitDingFlowAttachmentDto();
            attachmentDto.setName("附件");
            attachmentDto.setAttachmentFileList(new ArrayList<>());
            String mfsUrl = Global.instance.mfsUrl;
            String mfsRootPath = Global.instance.mfsRootPath;
            for (VehicleApplyFileDto fileDto : request.getVehicleApplyFileList()) {
                if (fileDto.getFileUrl().contains(mfsUrl)) {
                    fileDto.setFileUrl(fileDto.getFileUrl().replace(Global.instance.mfsUrl + "/", ""));
                }
                if (!fileDto.getFileUrl().contains(mfsRootPath)) {
                    fileDto.setFileUrl(mfsRootPath + "/" + fileDto.getFileUrl());
                }
                DingFlowAttachmentFileInfoDto dingFlowAttachmentFileInfoDto = new DingFlowAttachmentFileInfoDto();
                dingFlowAttachmentFileInfoDto.setFileName(fileDto.getFileName());
                dingFlowAttachmentFileInfoDto.setFile(new File(fileDto.getFileUrl()));
                attachmentDto.getAttachmentFileList().add(dingFlowAttachmentFileInfoDto);
            }
            createDingTalkWorkFlowRequest.setAttachmentInfo(attachmentDto);
        }
        try {
            String dingTalkNo = dingTalkService.createDingTalkWorkFlow(createDingTalkWorkFlowRequest);
            // 更新钉钉审批单号
            VehicleApplication updateVehicleApplication = new VehicleApplication();
            updateVehicleApplication.setId(longResultResponse.getData());
            updateVehicleApplication.setDingTalkNo(dingTalkNo);
            updateVehicleApplication.setSubmitDate(new Date());
            updateVehicleApplication.setDocumentStatus(DocumentStatusEnum.UNDER_REVIEW.getCode());
            tableVehicleApplicationService.updateSelectiveById(updateVehicleApplication, tokenUserInfo);
        } catch (Exception e) {
            log.error("提交钉钉审批单异常", e);
            throw new ServiceException("提交至钉钉审批单异常！");
        }

        return ResultResponse.success();
    }

    @Override
    public ResultResponse<Void> cancelApplication(Long id, TokenUserInfo tokenUserInfo) {
        VehicleApplication vehicleApplication = tableVehicleApplicationService.selectById(id);
        if (null == vehicleApplication) {
            throw new ServiceException("车辆申请单不存在！");
        }
        if (!vehicleApplication.getDocumentStatus().equals(DocumentStatusEnum.NOT_SUBMITTED.getCode()) &&
                !vehicleApplication.getDocumentStatus().equals(DocumentStatusEnum.REVIEW_REJECTED.getCode())) {
            throw new ServiceException("车辆申请单现状态无法作废！");
        }

        // 更新状态为已作废
        VehicleApplication updateVehicleApplication = new VehicleApplication();
        updateVehicleApplication.setId(id);
        updateVehicleApplication.setDocumentStatus(DocumentStatusEnum.CANCELLED.getCode());
        tableVehicleApplicationService.updateSelectiveById(updateVehicleApplication, tokenUserInfo);

        return ResultResponse.success();
    }

    @Override
    public ResultResponse<Void> withdrawApplication(Long id, TokenUserInfo tokenUserInfo) {
        VehicleApplication vehicleApplication = tableVehicleApplicationService.selectById(id);
        if (null == vehicleApplication) {
            throw new ServiceException("车辆申请单不存在！");
        }
        if (!vehicleApplication.getDocumentStatus().equals(DocumentStatusEnum.UNDER_REVIEW.getCode())) {
            throw new ServiceException("车辆申请单现状态无法撤回！");
        }

        // 调用钉钉撤回服务
        TerminateDingTalkProcessInstanceRequest processInstanceRequest = new TerminateDingTalkProcessInstanceRequest();
        processInstanceRequest.setSystem(true);
        processInstanceRequest.setProcessInstanceId(vehicleApplication.getDingTalkNo());
        processInstanceRequest.setRemark("申请撤回");
        dingTalkService.terminateProcessInstance(processInstanceRequest);

        // 更新状态为已作废
        VehicleApplication updateVehicleApplication = new VehicleApplication();
        updateVehicleApplication.setId(id);
        updateVehicleApplication.setDocumentStatus(DocumentStatusEnum.CANCELLED.getCode());
        tableVehicleApplicationService.updateSelectiveById(updateVehicleApplication, tokenUserInfo);

        return ResultResponse.success();
    }

    @Override
    public VehicleApplicationDetailResponse queryApplicationDetail(Long id) {
        // 申请单详情
        VehicleApplication vehicleApplication = tableVehicleApplicationService.selectById(id);
        VehicleApplicationDetailResponse vehicleApplicationDetailResponse = BeanUtil.copyProperties(vehicleApplication, VehicleApplicationDetailResponse.class);
        // 设置申请人为创建人
        vehicleApplicationDetailResponse.setApplyName(vehicleApplication.getCreateOperName());

        // 明细数据
        List<VehicleApplicationDetail> vehicleApplicationDetailList = tableVehicleApplicationDetailService.getVehicleApplicationDetailListByApplicationId(id);
        vehicleApplicationDetailResponse.setVehicleApplicationDetailList(BeanUtil.copyToList(vehicleApplicationDetailList, VehicleApplicationDetailDto.class));
        for (VehicleApplicationDetailDto vehicleApplicationDetailDto : vehicleApplicationDetailResponse.getVehicleApplicationDetailList()) {
            VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(vehicleApplicationDetailDto.getVehicleModelId());
            if (null != vehicleModelInfo) {
                vehicleApplicationDetailDto.setVehicleModelName(vehicleModelInfo.getVehicleModelName());
            }
        }

        // 附件
        List<VehicleApplyFileDto> fileDtoList = new ArrayList<>();
        List<VehicleApplyFile> vehicleApplyFileList = tableApplyFileService.queryFileList(id, FileBusinessTypeEnum.VEHICLE_MASTER_DATA_APPLICATION.getCode());
        vehicleApplyFileList.forEach(record -> {
            VehicleApplyFileDto fileDto = BeanUtil.copyProperties(record, VehicleApplyFileDto.class);
            String fileUrl = fileDto.getFileUrl().replace(Global.instance.mfsRootPath + "/", "");
            fileDto.setFileUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, fileUrl));
            fileDtoList.add(fileDto);
        });
        vehicleApplicationDetailResponse.setVehicleApplyFileList(fileDtoList);

        return vehicleApplicationDetailResponse;
    }

    @Override
    public void dingTalkResultProcess(String dingTalkNo, String dingTalkResult) {
        if (dingTalkResult.equals("agree")) {
            dingTalkResultAgree(dingTalkNo);
        } else if (dingTalkResult.equals("refuse")) {
            dingTalkResultRefuse(dingTalkNo);
        } else if (dingTalkResult.equals("terminate")) {
            dingTalkResultTerminate(dingTalkNo);
        }
    }

    @Override
    public List<VehicleListResponse> getImportVehicleApplicationDetail(BaseImportFileUrlRequest request) {
        List<ImportVin> readList = ExcelUtil.read(request.getFilePath(), 0, ImportVin.class);
        if (ObjectUtil.isEmpty(readList)) {
            throw new ServiceException("上传文件不能为空");
        }
        if (readList.size() > 1000) {
            throw new ServiceException("最大行数1000行");
        }

        // 获取车架号列表
        List<String> vinList = readList.stream().map(ImportVin::getVin).distinct().collect(Collectors.toList());
        if (vinList.size() != readList.size()) {
            throw new ServiceException("车架号不能重复");
        }
        List<VehicleListResponse> vehicleListResponseList = vehicleService.getVehicleListResponse(vinList);

        if (CollectionUtil.isNotEmpty(vehicleListResponseList)) {
            // 找出未查询到结果的车架号
            List<String> missingVins = vinList.stream()
                    .filter(vin -> vehicleListResponseList.stream().noneMatch(res -> vin.equals(res.getVin())))
                    .collect(Collectors.toList());

            if (!missingVins.isEmpty()) {
                throw new ServiceException("以下车架号未查询到结果：" + String.join(", ", missingVins));
            }

            // 检查productLine一致性
            if (CollectionUtil.isNotEmpty(vehicleListResponseList)) {
                Integer firstProductLine = vehicleListResponseList.get(0).getProductLine();
                List<String> mismatchVins = vehicleListResponseList.stream()
                        .filter(res -> !Objects.equals(res.getProductLine(), firstProductLine))
                        .map(VehicleListResponse::getVin)
                        .collect(Collectors.toList());

                if (!mismatchVins.isEmpty()) {
                    throw new ServiceException("以下车架号的业务线与第一条数据不一致：" + String.join(", ", mismatchVins));
                }
            }
        }

        return vehicleListResponseList;
    }

    /**
     * 钉钉审批通过处理
     *
     * @param dingTalkNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void dingTalkResultAgree(String dingTalkNo) {
        VehicleApplication vehicleApplication = tableVehicleApplicationService.selectByDingTalkNo(dingTalkNo);
        if (null == vehicleApplication) {
            throw new ServiceException("车辆主数据申请单不存在！");
        }

        TokenUserInfo dingTalkUserInfo = new TokenUserInfo();
        dingTalkUserInfo.setUserId(-1L);
        dingTalkUserInfo.setName("钉钉审批回调");
        // 变更申请单状态-审批通过
        VehicleApplication updateVehicleApplication = new VehicleApplication();
        updateVehicleApplication.setId(vehicleApplication.getId());
        updateVehicleApplication.setDocumentStatus(DocumentStatusEnum.REVIEW_APPROVED.getCode());
        tableVehicleApplicationService.updateSelectiveById(updateVehicleApplication, dingTalkUserInfo);

        // 车辆数据变更逻辑
        List<VehicleApplicationDetail> vehicleApplicationDetailList = tableVehicleApplicationDetailService.getVehicleApplicationDetailListByApplicationId(vehicleApplication.getId());
        for (VehicleApplicationDetail vehicleApplicationDetail : vehicleApplicationDetailList) {
            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vehicleApplicationDetail.getVin());
            if (null == vehicleInfo) {
                log.error("钉钉回调异常-车辆主数据申请单，钉钉单号：{}；车架号【{}】车辆信息不存在", dingTalkNo, vehicleApplicationDetail.getVin());
                continue;
            }

            VehicleInfo updateVehicleInfo = new VehicleInfo();
            updateVehicleInfo.setId(vehicleInfo.getId());

            String operateContent = StringUtils.EMPTY;
            // 车辆调拨
            if (vehicleApplication.getApplicationType() == 1) {
                updateVehicleInfo.setOwnOrganizationId(vehicleApplicationDetail.getOwnOrganizationIdUpdate());
                updateVehicleInfo.setUsageOrganizationId(vehicleApplicationDetail.getUsageOrganizationIdUpdate());
                operateContent += "实际运营公司（所属）从【" + ObjectValidUtil.formatStr(vehicleApplicationDetail.getOwnOrganizationName()) + "】变为【" + ObjectValidUtil.formatStr(vehicleApplicationDetail.getOwnOrganizationNameUpdate()) + "】；";
                operateContent += "实际运营公司（使用）从【" + ObjectValidUtil.formatStr(vehicleApplicationDetail.getUsageOrganizationName()) + "】变为【" + ObjectValidUtil.formatStr(vehicleApplicationDetail.getUsageOrganizationNameUpdate()) + "】；";
            }
            // 车辆转籍
            if (vehicleApplication.getApplicationType() == 2) {
                updateVehicleInfo.setAssetCompanyId(vehicleApplicationDetail.getAssetCompanyIdUpdate());
                updateVehicleInfo.setOwnOrganizationId(vehicleApplicationDetail.getOwnOrganizationIdUpdate());
                updateVehicleInfo.setUsageOrganizationId(vehicleApplicationDetail.getUsageOrganizationIdUpdate());
                operateContent += "资产所属公司从【" + ObjectValidUtil.formatStr(vehicleApplicationDetail.getAssetCompanyName()) + "】变为【" + ObjectValidUtil.formatStr(vehicleApplicationDetail.getAssetCompanyNameUpdate()) + "】；";
                operateContent += "实际运营公司（所属）从【" + ObjectValidUtil.formatStr(vehicleApplicationDetail.getOwnOrganizationName()) + "】变为【" + ObjectValidUtil.formatStr(vehicleApplicationDetail.getOwnOrganizationNameUpdate()) + "】；";
                operateContent += "实际运营公司（使用）从【" + ObjectValidUtil.formatStr(vehicleApplicationDetail.getUsageOrganizationName()) + "】变为【" + ObjectValidUtil.formatStr(vehicleApplicationDetail.getUsageOrganizationNameUpdate()) + "】；";
            }
            // 切换业务类型
            if (vehicleApplication.getApplicationType() == 3) {
                updateVehicleInfo.setProductLine(vehicleApplicationDetail.getProductLineUpdate());
                operateContent += "条线从【" + ProductLineEnum.getDesc(vehicleApplicationDetail.getProductLine()) + "】变为【" + ProductLineEnum.getDesc(vehicleApplicationDetail.getProductLineUpdate()) + "】；";
                updateVehicleInfo.setBusinessLine(vehicleApplicationDetail.getBusinessLineUpdate());
                operateContent += "业务线从【" + BusinessLineEnum.getDesc(vehicleApplicationDetail.getBusinessLine()) + "】变为【" + BusinessLineEnum.getDesc(vehicleApplicationDetail.getBusinessLineUpdate()) + "】；";
            }
            tableVehicleService.updateVehicle(updateVehicleInfo, dingTalkUserInfo);

            // 操作日志
            tableOperateLogService.insertLog(vehicleInfo.getId(), OperateLogBusinessTypeEnum.VEHICLE_MASTER_DATA.getCode(), null, operateContent, dingTalkUserInfo);
        }
    }

    /**
     * 钉钉审批拒绝处理
     *
     * @param dingTalkNo
     */
    public void dingTalkResultRefuse(String dingTalkNo) {
        VehicleApplication vehicleApplication = tableVehicleApplicationService.selectByDingTalkNo(dingTalkNo);
        if (null == vehicleApplication) {
            throw new ServiceException("车辆主数据申请单不存在！");
        }
        // 变更申请单状态-审批拒绝
        VehicleApplication updateVehicleApplication = new VehicleApplication();
        updateVehicleApplication.setId(vehicleApplication.getId());
        updateVehicleApplication.setDocumentStatus(DocumentStatusEnum.REVIEW_REJECTED.getCode());
        tableVehicleApplicationService.updateSelectiveById(updateVehicleApplication);
    }

    /**
     * 钉钉审批终止处理
     *
     * @param dingTalkNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void dingTalkResultTerminate(String dingTalkNo) {
        VehicleApplication vehicleApplication = tableVehicleApplicationService.selectByDingTalkNo(dingTalkNo);
        if (null == vehicleApplication) {
            throw new ServiceException("车辆主数据申请单不存在！");
        }

        TokenUserInfo dingTalkUserInfo = new TokenUserInfo();
        dingTalkUserInfo.setUserId(-1L);
        dingTalkUserInfo.setName("钉钉审批回调");

        // 变更申请单状态-已作废
        VehicleApplication updateVehicleApplication = new VehicleApplication();
        updateVehicleApplication.setId(vehicleApplication.getId());
        updateVehicleApplication.setDocumentStatus(DocumentStatusEnum.CANCELLED.getCode());
        tableVehicleApplicationService.updateSelectiveById(updateVehicleApplication, dingTalkUserInfo);
    }

    @Override
    public List<VehicleApplicationDetailDto> getImportVehicleDetailList(ImportVehicleApplicationFileUrlRequest request) {
        List<? extends ImportVehicleApplication> readList = new ArrayList<>();
        if (request.getApplicationType() == 1) {
            readList = ExcelUtil.read(request.getFilePath(), 0, ImportVehicleAllocate.class);
        }
        if (request.getApplicationType() == 2) {
            readList = ExcelUtil.read(request.getFilePath(), 0, ImportVehicleTransfer.class);
        }
        if (request.getApplicationType() == 3) {
            readList = ExcelUtil.read(request.getFilePath(), 0, ImportVehicleModifyBusiness.class);
        }

        if (ObjectUtil.isEmpty(readList)) {
            throw new ServiceException("上传文件不能为空");
        }
        if (readList.size() > 1000) {
            throw new ServiceException("最大行数1000行");
        }

        // 获取车架号列表
        List<String> vinList = readList.stream().map(ImportVehicleApplication::getVin).distinct().collect(Collectors.toList());
        if (vinList.size() != readList.size()) {
            throw new ServiceException("车架号不能重复");
        }
        List<VehicleListResponse> vehicleListResponseList = vehicleService.getVehicleListResponse(vinList);
        if (vehicleListResponseList.size() == 0) {
            throw new ServiceException("未查询到结果");
        }

        if (CollectionUtil.isNotEmpty(vehicleListResponseList)) {
            // 找出未查询到结果的车架号
            List<String> missingVins = vinList.stream()
                    .filter(vin -> vehicleListResponseList.stream().noneMatch(res -> vin.equals(res.getVin())))
                    .collect(Collectors.toList());

            if (!missingVins.isEmpty()) {
                throw new ServiceException("以下车架号未查询到结果：" + String.join(", ", missingVins));
            }

            // 检查productLine一致性
            if (CollectionUtil.isNotEmpty(vehicleListResponseList)) {
                Integer firstProductLine = vehicleListResponseList.get(0).getProductLine();
                List<String> mismatchVins = vehicleListResponseList.stream()
                        .filter(res -> !Objects.equals(res.getProductLine(), firstProductLine))
                        .map(VehicleListResponse::getVin)
                        .collect(Collectors.toList());

                if (!mismatchVins.isEmpty()) {
                    throw new ServiceException("以下车架号的条线与第一条数据不一致：" + String.join(", ", mismatchVins));
                }
            }
        }

        List<VehicleApplicationDetailDto> result = new ArrayList<>();
        for (ImportVehicleApplication importVehicleApplication : readList) {
            VehicleApplicationDetailDto dto = new VehicleApplicationDetailDto();

            VehicleListResponse vehicleListResponse = vehicleListResponseList.stream().filter(res -> res.getVin().equals(importVehicleApplication.getVin())).findFirst().orElse(null);
            if (null != vehicleListResponse) {
                BeanUtil.copyProperties(vehicleListResponse, dto);
                // 车辆调拨
                if (request.getApplicationType() == 1) {
                    OrgInfo ownOrgInfo = tableOrgInfoService.queryOrgInfoByName(((ImportVehicleAllocate) importVehicleApplication).getOwnOrganizationNameUpdate());
                    if (null != ownOrgInfo) {
                        dto.setOwnOrganizationIdUpdate(ownOrgInfo.getId());
                        dto.setOwnOrganizationNameUpdate(ownOrgInfo.getCompanyName());
                    }
                    OrgInfo usageOrgInfo = tableOrgInfoService.queryOrgInfoByName(((ImportVehicleAllocate) importVehicleApplication).getUsageOrganizationNameUpdate());
                    if (null != usageOrgInfo) {
                        dto.setUsageOrganizationIdUpdate(usageOrgInfo.getId());
                        dto.setUsageOrganizationNameUpdate(usageOrgInfo.getCompanyName());
                    }
                }
                // 车辆转籍
                if (request.getApplicationType() == 2) {
                    DataOwnerInfo dataOwnerInfo = tableDataOwnerInfoService.queryOwnerInfoByName(((ImportVehicleTransfer) importVehicleApplication).getAssetCompanyNameUpdate());
                    if (null != dataOwnerInfo) {
                        dto.setAssetCompanyIdUpdate(dataOwnerInfo.getId());
                        dto.setAssetCompanyNameUpdate(dataOwnerInfo.getName());
                    }
                    OrgInfo ownOrgInfo = tableOrgInfoService.queryOrgInfoByName(((ImportVehicleTransfer) importVehicleApplication).getOwnOrganizationNameUpdate());
                    if (null != ownOrgInfo) {
                        dto.setOwnOrganizationIdUpdate(ownOrgInfo.getId());
                        dto.setOwnOrganizationNameUpdate(ownOrgInfo.getCompanyName());
                    }
                    OrgInfo usageOrgInfo = tableOrgInfoService.queryOrgInfoByName(((ImportVehicleTransfer) importVehicleApplication).getUsageOrganizationNameUpdate());
                    if (null != usageOrgInfo) {
                        dto.setUsageOrganizationIdUpdate(usageOrgInfo.getId());
                        dto.setUsageOrganizationNameUpdate(usageOrgInfo.getCompanyName());
                    }
                }
                // 切换业务线
                if (request.getApplicationType() == 3) {
                    dto.setProductLineUpdate(ProductLineEnum.getCode(
                            ((ImportVehicleModifyBusiness) importVehicleApplication).getProductLineUpdateString()));
                    Integer businessLine = BusinessLineEnum.getCode(
                            ((ImportVehicleModifyBusiness) importVehicleApplication).getBusinessLineUpdateString());
                    if (dto.getProductLineUpdate() != null && dto.getProductLineUpdate() == 1) {
                        // 业务线只能为 1和5
                        if (businessLine == 1 || businessLine == 5) {
                            dto.setBusinessLineUpdate(businessLine);
                        }
                    } else if (dto.getProductLineUpdate() != null && dto.getProductLineUpdate() == 2) {
                        // 业务线只能为 非1
                        if (businessLine != 1) {
                            dto.setBusinessLineUpdate(businessLine);
                        }
                    }
                }
            }
            result.add(dto);
        }

        return result;
    }
}
