package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.dazhong.transportation.vlms.config.DingTalkConfig;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.constant.DingTalkConstant;
import com.dazhong.transportation.vlms.database.*;
import com.dazhong.transportation.vlms.dto.CommitDingFlowAttachmentDto;
import com.dazhong.transportation.vlms.dto.DingFlowAttachmentFileInfoDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkDetailResponse;
import com.dazhong.transportation.vlms.dto.response.TransferFixedApplyResponse;
import com.dazhong.transportation.vlms.dto.response.TransferFixedDetailsResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleTransferFixedResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.*;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.*;
import com.dazhong.transportation.vlms.service.IDataDictService;
import com.dazhong.transportation.vlms.service.IDingTalkService;
import com.dazhong.transportation.vlms.service.ITransferFixedService;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class TransferFixedServiceImpl implements ITransferFixedService {

    @Autowired
    private TableTransferFixedApplyService tableTransferFixedApplyService;

    @Autowired
    private TableTransferFixedDetailsService tableTransferFixedDetailsService;

    @Autowired
    private TableApplyFileService tableApplyFileService;

    @Autowired
    private TableVehicleService tableVehicleService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Autowired
    private TableVehicleDecorationService tableVehicleDecorationService;

    @Autowired
    private IDataDictService dataDictService;

    @Autowired
    private IDingTalkService dingTalkService;

    @Autowired
    private DingTalkConfig dingTalkConfig;

    @Autowired
    private RedisUtils redisUtils;


    @Override
    public PageResponse searchVehicleTransferFixedApply(SearchTransferFixedRequest request, TokenUserInfo tokenUserInfo) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehicleTransferFixedApply> list = tableTransferFixedApplyService.searchVehicleTransferFixedApply(request, tokenUserInfo);
        PageInfo<VehicleTransferFixedApply> pageInfo = new PageInfo<>(list);
        // 返回结果
        List<TransferFixedApplyResponse> responseList = new ArrayList<>();
        list.forEach(res -> {
            TransferFixedApplyResponse response = BeanUtil.copyProperties(res, TransferFixedApplyResponse.class);
            responseList.add(response);
        });
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return pageResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse addVehicleTransferFixed(AddFixedAssetVehicleRequest request, TokenUserInfo tokenUserInfo) {
        // 校验车辆信息是否存在 判断资产状态
        List<FixedAssetVehicleDto> vehicleList = request.getVehicleList();
        Map<String, FixedAssetVehicleDto> requestVehicleMap = vehicleList.stream().collect(Collectors.toMap(FixedAssetVehicleDto::getVin, vehicleInfo -> vehicleInfo));

        List<String> vinList = vehicleList.stream().map(FixedAssetVehicleDto::getVin).collect(Collectors.toList());
        List<VehicleTransferFixedResponse> vehicleInfoList = getVehicleTransferFixedResponse(vinList);
        Map<String, VehicleTransferFixedResponse> vehicleMap = vehicleInfoList.stream().collect(Collectors.toMap(VehicleTransferFixedResponse::getVin, vehicleInfo -> vehicleInfo));

        // 查询审批中的车辆信息
        List<VehicleTransferFixedDetails> transferFixedDetailsList = tableTransferFixedDetailsService.queryVehicleApprovalRecordList();
        Map<String, VehicleTransferFixedDetails> transferFixedDetailsMap = transferFixedDetailsList.stream().collect(Collectors.toMap(VehicleTransferFixedDetails::getVin, details -> details));

        // 新增车辆校验
        for (String vin : vinList) {
            VehicleTransferFixedResponse vehicleInfo = vehicleMap.get(vin);
            if (vehicleInfo == null) {
                return ResultResponse.businessFailed(StrUtil.format("{}-车辆信息不存在", vin));
            }
            //资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付） 4-待报废（未交付） 5-已处置 6-已报废
            int propertyStatus = vehicleInfo.getPropertyStatus();
            if (propertyStatus != 0) {
                return ResultResponse.businessFailed(StrUtil.format("{}-车辆资产状态不是在建工程，不能转固", vin));
            }
            VehicleTransferFixedDetails details = transferFixedDetailsMap.get(vin);
            if (details == null) {
                continue;
            }
            // 查询车辆是否有审批中的转固申请
            VehicleTransferFixedApply transferFixedApply = tableTransferFixedApplyService.selectById(details.getTransferFixedApplyId());
            if (transferFixedApply == null) {
                continue;
            }
            // 转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
            int applyStatus = transferFixedApply.getApplyStatus();
            if (applyStatus != BizConstant.ApplyStatus.applyStatus_4 &&
                    applyStatus != BizConstant.ApplyStatus.applyStatus_5) {
                return ResultResponse.businessFailed(StrUtil.format("{}-车架号有存在中的转固审批信息", vin));
            }
        }
        //操作类型 1-保存 2-提交
        int operateType = request.getOperateType();
        String applyNo = redisUtils.generateUniqueId("ZG");
        VehicleTransferFixedApply apply = new VehicleTransferFixedApply();
        apply.setApplyNo(applyNo);
        apply.setApplyStatus(operateType == 1 ? BizConstant.ApplyStatus.applyStatus_1 : BizConstant.ApplyStatus.applyStatus_2);
        apply.setApplyName(request.getApplyName());
        apply.setApplyRemark(request.getApplyRemark());
        apply.setApplyOrgId(request.getApplyOrgId());
        apply.setOwnerId(request.getOwnerId());
        apply.setTransferQuantity(vinList.size());
        apply.setDepartmentCode(request.getDepartmentCode());
        apply.setDepartmentName(request.getDepartmentName());
        apply.setApplicantDepartmentCode(String.valueOf(request.getOriginatorDeptId()));
        apply.setApplicantDepartmentName(request.getOriginatorDeptName());
        tableTransferFixedApplyService.insert(apply, tokenUserInfo);

        vehicleList.forEach(vehicle -> {
            VehicleTransferFixedDetails details = new VehicleTransferFixedDetails();
            details.setTransferFixedApplyId(apply.getId());
            details.setVin(vehicle.getVin());
            details.setProductLine(vehicle.getProductLine());
            details.setBusinessLine(vehicle.getBusinessLine());
            details.setApprovalStatus(1);
            tableTransferFixedDetailsService.insert(details, tokenUserInfo);
        });

        // 新增转固附件信息
        List<VehicleApplyFileDto> fileList = request.getFileList();
        if (CollectionUtil.isNotEmpty(fileList)) {
            List<VehicleApplyFile> applyFileList = BeanUtil.copyToList(fileList, VehicleApplyFile.class);
            for (VehicleApplyFile applyFile : applyFileList) {
                applyFile.setForeignId(apply.getId());
                applyFile.setBusinessType(FileBusinessTypeEnum.FIXED_ASSET_APPLICATION.getCode());
                tableApplyFileService.insert(applyFile, tokenUserInfo);
            }
        }

        // 提交转固审批
        if (operateType == 2) {
            for (VehicleTransferFixedResponse response : vehicleInfoList) {
                FixedAssetVehicleDto dto = requestVehicleMap.get(response.getVin());
                if (dto != null) {
                    response.setProductLine(dto.getProductLine());
                    response.setBusinessLine(dto.getBusinessLine());
                }
            }

            CreateDingTalkWorkFlowRequest createDingTalkWorkFlowRequest = getDingTalkWorkFlow(apply, vehicleInfoList);
            try {
                if (CollectionUtil.isNotEmpty(fileList)) {
                    CommitDingFlowAttachmentDto attachmentInfo = new CommitDingFlowAttachmentDto();
                    attachmentInfo.setName("附件");
                    List<DingFlowAttachmentFileInfoDto> attachmentFileList = new ArrayList<>();
                    String mfsRootPath = Global.instance.mfsRootPath;
                    for (VehicleApplyFileDto dto : fileList) {
                        DingFlowAttachmentFileInfoDto fileInfoDto = new DingFlowAttachmentFileInfoDto();
                        String fileUrl = mfsRootPath + "/" + dto.getFileUrl();
                        File file = new File(fileUrl);
                        fileInfoDto.setFile(file);
                        fileInfoDto.setFileName(StrUtil.format("{}-{}", TransferFixedFileTypeEnum.getDesc(dto.getFileType()), file.getName()));
                        attachmentFileList.add(fileInfoDto);
                    }
                    attachmentInfo.setAttachmentFileList(attachmentFileList);
                    createDingTalkWorkFlowRequest.setAttachmentInfo(attachmentInfo);
                }
                createDingTalkWorkFlowRequest.setOriginatorUserId(tokenUserInfo.getDingTalkNum());
                createDingTalkWorkFlowRequest.setOriginatorDeptId(request.getOriginatorDeptId());
                String dingTalkNo = dingTalkService.createDingTalkWorkFlow(createDingTalkWorkFlowRequest);
                apply.setApprovalNumber(dingTalkNo);
                tableTransferFixedApplyService.updateVehicleTransferFixedApply(apply, tokenUserInfo);
            } catch (ServiceException e) {
                log.error("提交钉钉转固审批单异常", e);
                throw new ServiceException(StrUtil.format("提交转固审批单-{}", e.getMessage()));
            }
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse updateVehicleTransferFixed(UpdateFixedAssetVehicleRequest request, TokenUserInfo tokenUserInfo) {
        // 校验转固申请信息
        VehicleTransferFixedApply transferFixedApply = tableTransferFixedApplyService.queryVehicleTransferFixedApply(request.getApplyNo());
        if (transferFixedApply == null) {
            return ResultResponse.businessFailed(StrUtil.format("{}-转固申请信息不存在", request.getApplyNo()));
        }
        // 转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
        int applyStatus = transferFixedApply.getApplyStatus();
        if (applyStatus != BizConstant.ApplyStatus.applyStatus_1 &&
                applyStatus != BizConstant.ApplyStatus.applyStatus_4) {
            return ResultResponse.businessFailed("申请状态不满足条件，不能修改");
        }
        List<FixedAssetVehicleDto> vehicleList = request.getVehicleList();
        Map<String, FixedAssetVehicleDto> requestVehicleMap = vehicleList.stream().collect(Collectors.toMap(FixedAssetVehicleDto::getVin, vehicleInfo -> vehicleInfo));

        // 导入车辆校验
        List<String> vinList = vehicleList.stream().map(FixedAssetVehicleDto::getVin).collect(Collectors.toList());
        List<VehicleTransferFixedResponse> vehicleInfoList = getVehicleTransferFixedResponse(vinList);
        Map<String, VehicleTransferFixedResponse> vehicleMap = vehicleInfoList.stream().collect(Collectors.toMap(VehicleTransferFixedResponse::getVin, response -> response));

        // 查询审批中的车辆信息
        List<VehicleTransferFixedDetails> transferFixedDetailsList = tableTransferFixedDetailsService.queryVehicleApprovalRecordList();
        Map<String, VehicleTransferFixedDetails> transferFixedDetailsMap = transferFixedDetailsList.stream().collect(Collectors.toMap(VehicleTransferFixedDetails::getVin, details -> details));

        /*校验车辆是否可以转固*/
        for (String vin : vinList) {
            VehicleTransferFixedResponse vehicleInfo = vehicleMap.get(vin);
            if (vehicleInfo == null) {
                return ResultResponse.businessFailed(StrUtil.format("{}-车辆信息不存在", vin));
            }
            //资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付）  4-待报废（未交付） 5-已处置 6-已报废
            int propertyStatus = vehicleInfo.getPropertyStatus();
            if (propertyStatus != 0) {
                return ResultResponse.businessFailed(StrUtil.format("{}-车辆资产状态不是在建工程，不能转固", vin));
            }
            // 查询车辆是否有审批中的转固申请
            VehicleTransferFixedDetails details = transferFixedDetailsMap.get(vin);
            if (details == null) {
                continue;
            }
            if (details.getTransferFixedApplyId() == transferFixedApply.getId()) {
                continue;
            }
            // 查询车辆关联的转固申请信息
            VehicleTransferFixedApply apply = tableTransferFixedApplyService.selectById(details.getTransferFixedApplyId());
            if (apply == null) {
                continue;
            }
            // 转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
            applyStatus = apply.getApplyStatus();
            if (applyStatus != BizConstant.ApplyStatus.applyStatus_4 &&
                    applyStatus != BizConstant.ApplyStatus.applyStatus_5) {
                return ResultResponse.businessFailed(StrUtil.format("{}-车架号有存在中的转固审批信息", vin));
            }
        }
        // 删除车辆转固记录 重新添加
        tableTransferFixedDetailsService.deleteTransferFixedDetailsByApplyId(transferFixedApply.getId());
        vehicleList.forEach(vehicle -> {
            VehicleTransferFixedDetails details = new VehicleTransferFixedDetails();
            details.setTransferFixedApplyId(transferFixedApply.getId());
            details.setVin(vehicle.getVin());
            details.setProductLine(vehicle.getProductLine());
            details.setBusinessLine(vehicle.getBusinessLine());
            details.setApprovalStatus(1);
            tableTransferFixedDetailsService.insert(details, tokenUserInfo);
        });

        // 操作类型 1-保存 2-提交
        int operateType = request.getOperateType();
        // 修改转固信息
        transferFixedApply.setTransferQuantity(vehicleInfoList.size());
        transferFixedApply.setApplyStatus(operateType == 1 ? BizConstant.ApplyStatus.applyStatus_1 : BizConstant.ApplyStatus.applyStatus_2);
        transferFixedApply.setApplyName(request.getApplyName());
        transferFixedApply.setApplyRemark(request.getApplyRemark());
        transferFixedApply.setApplyOrgId(request.getApplyOrgId());
        transferFixedApply.setOwnerId(request.getOwnerId());
        transferFixedApply.setDepartmentCode(request.getDepartmentCode());
        transferFixedApply.setDepartmentName(request.getDepartmentName());
        transferFixedApply.setApplicantDepartmentCode(String.valueOf(request.getOriginatorDeptId()));
        transferFixedApply.setApplicantDepartmentName(request.getOriginatorDeptName());
        tableTransferFixedApplyService.updateVehicleTransferFixedApply(transferFixedApply, tokenUserInfo);

        tableApplyFileService.deleteApplyFile(transferFixedApply.getId(), 2);
        // 附件信息
        List<VehicleApplyFileDto> fileList = request.getFileList();
        if (CollectionUtil.isNotEmpty(fileList)) {
            String mfsUrl = Global.instance.mfsUrl;
            List<VehicleApplyFile> applyFileList = BeanUtil.copyToList(fileList, VehicleApplyFile.class);
            for (VehicleApplyFile applyFile : applyFileList) {
                String fileUrl = applyFile.getFileUrl();
                if (fileUrl.contains(mfsUrl)) {
                    applyFile.setFileUrl(fileUrl.replace(mfsUrl + "/", ""));
                }
                applyFile.setForeignId(transferFixedApply.getId());
                applyFile.setBusinessType(FileBusinessTypeEnum.FIXED_ASSET_APPLICATION.getCode());
                tableApplyFileService.insert(applyFile, tokenUserInfo);
            }
        }
        //提交转固审批
        if (operateType == 2) {
            for (VehicleTransferFixedResponse response : vehicleInfoList) {
                FixedAssetVehicleDto dto = requestVehicleMap.get(response.getVin());
                if (dto != null) {
                    response.setProductLine(dto.getProductLine());
                    response.setBusinessLine(dto.getBusinessLine());
                }
            }

            CreateDingTalkWorkFlowRequest createDingTalkWorkFlowRequest = getDingTalkWorkFlow(transferFixedApply, vehicleInfoList);
            try {
                if (CollectionUtil.isNotEmpty(fileList)) {
                    String mfsUrl = Global.instance.mfsUrl;
                    String mfsRootPath = Global.instance.mfsRootPath;
                    CommitDingFlowAttachmentDto attachmentInfo = new CommitDingFlowAttachmentDto();
                    attachmentInfo.setName("附件");
                    List<DingFlowAttachmentFileInfoDto> attachmentFileList = new ArrayList<>();
                    for (VehicleApplyFileDto dto : fileList) {
                        DingFlowAttachmentFileInfoDto fileInfoDto = new DingFlowAttachmentFileInfoDto();
                        String fileUrl = dto.getFileUrl();
                        if (fileUrl.contains(mfsUrl)) {
                            fileUrl = StrUtil.format("{}{}", mfsRootPath, fileUrl.replace(mfsUrl, ""));
                        }
                        File file = new File(fileUrl);
                        fileInfoDto.setFile(file);
                        fileInfoDto.setFileName(StrUtil.format("{}-{}", TransferFixedFileTypeEnum.getDesc(dto.getFileType()), dto.getFileName()));
                        attachmentFileList.add(fileInfoDto);
                    }
                    attachmentInfo.setAttachmentFileList(attachmentFileList);
                    createDingTalkWorkFlowRequest.setAttachmentInfo(attachmentInfo);
                }
                createDingTalkWorkFlowRequest.setOriginatorUserId(tokenUserInfo.getDingTalkNum());
                createDingTalkWorkFlowRequest.setOriginatorDeptId(request.getOriginatorDeptId());
                String dingTalkNo = dingTalkService.createDingTalkWorkFlow(createDingTalkWorkFlowRequest);
                transferFixedApply.setApprovalNumber(dingTalkNo);
            } catch (ServiceException e) {
                log.error("提交钉钉转固审批单异常", e);
                throw new ServiceException(StrUtil.format("提交转固审批单-{}", e.getMessage()));
            }
        }
        tableTransferFixedApplyService.updateVehicleTransferFixedApply(transferFixedApply, tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse editTransferFixedStatus(EditTransferFixedStatusRequest request, TokenUserInfo tokenUserInfo) {
        VehicleTransferFixedApply transferFixedApply = tableTransferFixedApplyService.queryVehicleTransferFixedApply(request.getApplyNo());
        if (transferFixedApply == null) {
            return ResultResponse.businessFailed("转固申请信息不存在");
        }
        // 转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
        int applyStatus = transferFixedApply.getApplyStatus();
        if (applyStatus == BizConstant.ApplyStatus.applyStatus_5) {
            return ResultResponse.success();
        }
        //操作类型 1-作废 2-撤回
        int operateType = request.getOperateType();
        if (operateType == 1) {
            // 未提交、审批拒绝 可以作废
            if (applyStatus != BizConstant.ApplyStatus.applyStatus_1 &&
                    applyStatus != BizConstant.ApplyStatus.applyStatus_4) {
                return ResultResponse.businessFailed("转固申请状态不满足条件，无法作废");
            }
        } else if (operateType == 2) {
            if (applyStatus != BizConstant.ApplyStatus.applyStatus_2) {
                return ResultResponse.businessFailed("转固申请状态不满足条件，无法撤回");
            }
            if (StringUtils.isBlank(transferFixedApply.getApprovalNumber())) {
                return ResultResponse.businessFailed("钉钉审批单号不存在，无法撤回");
            }
            //调用钉钉撤回服务
            TerminateDingTalkProcessInstanceRequest processInstanceRequest = new TerminateDingTalkProcessInstanceRequest();
            processInstanceRequest.setSystem(true);
            processInstanceRequest.setProcessInstanceId(transferFixedApply.getApprovalNumber());
            processInstanceRequest.setRemark("转固申请撤回");
            dingTalkService.terminateProcessInstance(processInstanceRequest);
        }
        transferFixedApply.setApplyStatus(BizConstant.ApplyStatus.applyStatus_5);
        tableTransferFixedApplyService.updateVehicleTransferFixedApply(transferFixedApply, tokenUserInfo);
        tableTransferFixedDetailsService.updateApprovalStatus(transferFixedApply.getId(), 3);
        return ResultResponse.success();
    }

    @Override
    public ResultResponse queryTransferFixedDetail(String applyNo) {
        VehicleTransferFixedApply transferFixedApply = tableTransferFixedApplyService.queryVehicleTransferFixedApply(applyNo);
        if (transferFixedApply == null) {
            return ResultResponse.businessFailed("转固申请信息不存在");
        }

        Map<Integer, DataOwnerInfo> ownerInfoMap = dataDictService.getOwnerMap();

        TransferFixedDetailsResponse detailsResponse = new TransferFixedDetailsResponse();
        detailsResponse.setApplyNo(transferFixedApply.getApplyNo());
        detailsResponse.setOwnerId(transferFixedApply.getOwnerId());
        if (transferFixedApply.getOwnerId() != null && ownerInfoMap.get(transferFixedApply.getOwnerId()) != null) {
            detailsResponse.setOwnerName(ownerInfoMap.get(transferFixedApply.getOwnerId()).getName());
        }
        detailsResponse.setApplyOrgId(transferFixedApply.getApplyOrgId());
        detailsResponse.setApprovalNumber(transferFixedApply.getApprovalNumber());
        detailsResponse.setApplyStatus(transferFixedApply.getApplyStatus());
        detailsResponse.setApplyName(transferFixedApply.getApplyName());
        detailsResponse.setApplyRemark(transferFixedApply.getApplyRemark());
        detailsResponse.setTransferQuantity(transferFixedApply.getTransferQuantity());
        detailsResponse.setApplyUser(transferFixedApply.getApplyUser());
        detailsResponse.setDepartmentCode(transferFixedApply.getDepartmentCode());
        detailsResponse.setDepartmentName(transferFixedApply.getDepartmentName());
        if (StringUtils.isNotBlank(transferFixedApply.getApplicantDepartmentCode())){
            detailsResponse.setOriginatorDeptId(Long.valueOf(transferFixedApply.getApplicantDepartmentCode()));
        }
        detailsResponse.setOriginatorDeptName(transferFixedApply.getApplicantDepartmentName());
        OrgInfo orgInfo = tableOrgInfoService.queryOrgInfoById(transferFixedApply.getApplyOrgId());
        if (orgInfo != null) {
            detailsResponse.setApplyOrgName(orgInfo.getCompanyName());
        }
        // 查询转固车辆信息
        List<VehicleTransferFixedDetails> detailsList = tableTransferFixedDetailsService.queryTransferFixedDetailsByApplyId(transferFixedApply.getId());
        Map<String, VehicleTransferFixedDetails> detailsMap = detailsList.stream().collect(Collectors.toMap(VehicleTransferFixedDetails::getVin, details -> details));


        // 查询车辆信息
        List<String> vinList = detailsList.stream().map(VehicleTransferFixedDetails::getVin).collect(Collectors.toList());
        List<VehicleTransferFixedResponse> list = getVehicleTransferFixedResponse(vinList);
        list.forEach(response -> {
            VehicleTransferFixedDetails details = detailsMap.get(response.getVin());
            if (details != null) {
                response.setProductLine(details.getProductLine());
                response.setBusinessLine(details.getBusinessLine());
            }
        });
        detailsResponse.setVehicleList(list);

        // 查询附件信息
        List<VehicleApplyFile> attachmentList = tableApplyFileService.queryFileList(transferFixedApply.getId(), 2);
        if (CollectionUtil.isNotEmpty(attachmentList)) {
            List<VehicleApplyFileDto> fileDtoList = BeanUtil.copyToList(attachmentList, VehicleApplyFileDto.class);
            // 拼接图片地址
            fileDtoList.forEach(file -> {
                String vehicleLicenseUrl = file.getFileUrl().replace(Global.instance.mfsRootPath + "/", "");
                file.setFileUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, vehicleLicenseUrl));
            });
            detailsResponse.setFileList(fileDtoList);
        }
        return ResultResponse.success(detailsResponse);
    }

    @Override
    public List<VehicleTransferFixedResponse> getVehicleTransferFixedResponse(List<String> vinList) {
        List<VehicleTransferFixedResponse> list = tableVehicleService.searchVehicleTransferFixed(vinList);

        //查询车辆装潢信息
        List<VehicleDecoration> decorationList = tableVehicleDecorationService.queryDecorationList(vinList);
        // 查询组织信息
        Map<Integer, DataOwnerInfo> ownerInfoMap = dataDictService.getOwnerMap();
        // 查询组织信息
        Map<Long, OrgInfo> orgMap = dataDictService.getOrgMap();

        // decorationList 车架号分组
        Map<String, List<VehicleDecoration>> decorationMap = decorationList.stream().collect(Collectors.groupingBy(VehicleDecoration::getVin));
        for (VehicleTransferFixedResponse response : list) {
            response.setISRegistered(StringUtils.isNotBlank(response.getLicensePlate()) ? 1 : 2);
            response.setIsDecoration(decorationMap.get(response.getVin()) != null ? 1 : 2);
            response.setIsOther(response.getTotalPrice() != null ? 1 : 2);

            //资产所属公司名称
            if (response.getAssetCompanyId() != null && ownerInfoMap.get(response.getAssetCompanyId()) != null) {
                response.setAssetCompanyName(ownerInfoMap.get(response.getAssetCompanyId()).getName());
            }
            //所属资产机构
            if (response.getOwnOrganizationId() != null && orgMap.get(response.getOwnOrganizationId()) != null) {
                response.setOwnOrganizationName(orgMap.get(response.getOwnOrganizationId()).getCompanyName());
            }
            //使用机构
            if (response.getUsageOrganizationId() != null && orgMap.get(response.getUsageOrganizationId()) != null) {
                response.setUsageOrganizationName(orgMap.get(response.getUsageOrganizationId()).getCompanyName());
            }
        }
        return list;
    }

    @Override
    public ResultResponse refreshTransferFixed(RefreshDingTalkApprovalRequest request, TokenUserInfo tokenUserInfo) {
        VehicleTransferFixedApply transferFixedApply = tableTransferFixedApplyService.queryTransferFixedApplyByApprovalNumber(request.getApprovalNumber());
        if (transferFixedApply == null) {
            return ResultResponse.success();
        }
        // 转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
        int applyStatus = transferFixedApply.getApplyStatus();
        if (applyStatus != BizConstant.ApplyStatus.applyStatus_2) {
            return ResultResponse.success();
        }
        GetDingTalkWorkFlowRequest queryFormInstanceRequest = new GetDingTalkWorkFlowRequest();
        queryFormInstanceRequest.setInstanceId(transferFixedApply.getApprovalNumber());
        GetDingTalkDetailResponse getDingTalkDetailResponse = dingTalkService.getDingTalkDetailFlow(queryFormInstanceRequest);
        String workFlowResult = getDingTalkDetailResponse.getWorkFlowResult();
        String result = DingTalkConstant.dingTalkResultMap.get(workFlowResult);
        if (StringUtils.isBlank(result)) {
            return ResultResponse.success();
        }
        // 调用审批结果逻辑
        dingTalkResultProcess(transferFixedApply.getApprovalNumber(), result);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dingTalkResultProcess(String requestNo, String dingTalkResult) {
        VehicleTransferFixedApply transferFixedApply = tableTransferFixedApplyService.queryTransferFixedApplyByApprovalNumber(requestNo);
        if (transferFixedApply == null) {
            log.error("转固申请信息不存在，requestNo:{}", requestNo);
            return;
        }
        // 转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
        int applyStatus = transferFixedApply.getApplyStatus();
        if (applyStatus != BizConstant.ApplyStatus.applyStatus_2) {
            return;
        }

        TokenUserInfo tokenUserInfo = new TokenUserInfo();
        tokenUserInfo.setUserId(-1L);
        tokenUserInfo.setName("钉钉审批回调");
        if (dingTalkResult.equals("agree")) {
            List<VehicleTransferFixedDetails> detailsList = tableTransferFixedDetailsService.queryTransferFixedDetailsByApplyId(transferFixedApply.getId());
            Map<String, VehicleTransferFixedDetails> detailsMap = detailsList.stream().collect(Collectors.toMap(VehicleTransferFixedDetails::getVin, transferFixedDetails -> transferFixedDetails));

            List<String> vinList = detailsList.stream().map(VehicleTransferFixedDetails::getVin).collect(Collectors.toList());
            List<VehicleInfo> vehicleList = tableVehicleService.queryVehicleByVinList(vinList);

            for (VehicleInfo vehicleInfo : vehicleList) {
                VehicleTransferFixedDetails details = detailsMap.get(vehicleInfo.getVin());
                vehicleInfo.setPropertyStatus(PropertyStatusEnum.FIXED_ASSET.getCode());
                vehicleInfo.setOperatingStatus(OperatingStatusEnum.PENDING.getCode());
                vehicleInfo.setProductLine(details.getProductLine() != null ? details.getProductLine() : vehicleInfo.getProductLine());
                vehicleInfo.setBusinessLine(details.getBusinessLine() != null ? details.getBusinessLine() : vehicleInfo.getBusinessLine());
                tableVehicleService.updateVehicle(vehicleInfo, tokenUserInfo);
                // 操作记录
                tableOperateLogService.insertLog(vehicleInfo.getId(), BizConstant.BusinessType.businessType_5, null, StrUtil.format("转固审批通过,车辆转为固定资产转固审批任务号-{}", transferFixedApply.getApplyNo()), tokenUserInfo);
            }
            transferFixedApply.setApplyStatus(BizConstant.ApplyStatus.applyStatus_3);
            tableTransferFixedApplyService.updateVehicleTransferFixedApply(transferFixedApply, tokenUserInfo);
            tableTransferFixedDetailsService.updateApprovalStatus(transferFixedApply.getId(), 2);
        } else if (dingTalkResult.equals("refuse")) {
            transferFixedApply.setApplyStatus(BizConstant.ApplyStatus.applyStatus_4);
            tableTransferFixedApplyService.updateVehicleTransferFixedApply(transferFixedApply, tokenUserInfo);
            tableTransferFixedDetailsService.updateApprovalStatus(transferFixedApply.getId(), 3);
        }
    }

    /**
     * 构建审批参数
     *
     * @param apply
     * @param vehicleList
     * @return
     */
    private CreateDingTalkWorkFlowRequest getDingTalkWorkFlow(VehicleTransferFixedApply apply, List<VehicleTransferFixedResponse> vehicleList) {
        //查询字典表相关信息
        List<String> systemCodeList = Arrays.asList(
                //组织机构
                DataDictEnum.ORGANIZATION.getValue(),
                //组织机构
                DataDictEnum.OWNER.getValue()
        );
        Map<String, Map<Integer, String>> dataMaintainDictMap = dataDictService.getDataMaintainDictMap(systemCodeList);
        Map<Integer, String> orgMap = dataMaintainDictMap.get(DataDictEnum.ORGANIZATION.getValue());
        // 车辆拥有公司
        Map<Integer, String> ownerMap = dataMaintainDictMap.get(DataDictEnum.OWNER.getValue());

        Map<String, String> map = new HashMap<>();
        map.put("单据标题", apply.getApplyName());
        map.put("单据所属资产公司", ObjectValidUtil.formatStr(ownerMap.get(apply.getOwnerId())));
        map.put("单据所属机构", ObjectValidUtil.formatStr(orgMap.get(apply.getApplyOrgId().intValue())));
        map.put("总车辆数", String.valueOf(vehicleList.size()));
        map.put("转固说明", ObjectValidUtil.formatStr(apply.getApplyRemark()));
        map.put("资产公司所属部门", ObjectValidUtil.formatStr(apply.getDepartmentCode()));

        CreateDingTalkWorkFlowRequest createDingTalkWorkFlowRequest = new CreateDingTalkWorkFlowRequest();
        createDingTalkWorkFlowRequest.setProcessCode(dingTalkConfig.getVehicleTransferFixedProcessCode());
        List<List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails>> result = new ArrayList<>();
        vehicleList.forEach(response -> {
            List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails> detailsList = new ArrayList<>();
            StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("资产编号").setValue(ObjectValidUtil.formatStr(response.getVehicleAssetId()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("车架号").setValue(ObjectValidUtil.formatStr(response.getVin()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("车牌号").setValue(ObjectValidUtil.formatStr(response.getLicensePlate()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("车型").setValue(ObjectValidUtil.formatStr(response.getVehicleModelName()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("条线").setValue(ObjectValidUtil.formatStr(ProductLineEnum.getDesc(response.getProductLine())));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("业务类型").setValue(ObjectValidUtil.formatStr(BusinessLineEnum.getDesc(response.getBusinessLine())));
            detailsList.add(formComponentValue);

//            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
//            formComponentValue.setName("采购申请钉钉号").setValue(ObjectValidUtil.formatStr(""));
//            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            String receiptDate = DateTimeUtils.dateToString(response.getReceiptDate(), DateTimeUtils.DATE_TYPE3);
            formComponentValue.setName("收货日期").setValue(ObjectValidUtil.formatStr(receiptDate));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("资产所属公司").setValue(ObjectValidUtil.formatStr(response.getAssetCompanyName()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("实际运营公司（所属）").setValue(ObjectValidUtil.formatStr(response.getOwnOrganizationName()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("实际运营公司（使用）").setValue(ObjectValidUtil.formatStr(response.getUsageOrganizationName()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("上牌信息").setValue(response.getISRegistered() == 1 ? "已登记" : "未登记");
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("装潢信息").setValue(response.getIsDecoration() == 1 ? "已登记" : "未登记");
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("其他信息").setValue(response.getIsOther() == 1 ? "已登记" : "未登记");
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("使用年限").setValue(String.valueOf(response.getUsageAgeLimit()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("折旧年限").setValue(String.valueOf(response.getDepreciationAgeLimit()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("裸车价（元）").setValue(ObjectValidUtil.formatStr(response.getPurchasePrice()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("购置税（元）").setValue(ObjectValidUtil.formatStr(response.getPurchaseTax()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("牌照费（元）").setValue(ObjectValidUtil.formatStr(response.getLicensePlatePrice()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("上牌杂费（元）").setValue(ObjectValidUtil.formatStr(response.getLicensePlateOtherPrice()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("装潢费（元）").setValue(ObjectValidUtil.formatStr(response.getUpholsterPrice()));
            detailsList.add(formComponentValue);

            formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails();
            formComponentValue.setName("购置总价（元）").setValue(ObjectValidUtil.formatStr(response.getTotalPrice()));
            detailsList.add(formComponentValue);
            result.add(detailsList);
        });
        map.put("明细数据", JSON.toJSONString(result));
        createDingTalkWorkFlowRequest.setProcessComponentValues(map);
        return createDingTalkWorkFlowRequest;
    }
}
