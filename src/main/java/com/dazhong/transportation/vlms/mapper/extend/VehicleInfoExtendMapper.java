package com.dazhong.transportation.vlms.mapper.extend;

import com.dazhong.transportation.vlms.dto.ExportAssetVehicleInfoDto;
import com.dazhong.transportation.vlms.dto.SyncAssetVehicleInfoDto;
import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleAssetDto;
import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleDto;
import com.dazhong.transportation.vlms.dto.response.InboundVehicleResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleListResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleTransferFixedResponse;
import com.dazhong.transportation.vlms.mapper.VehicleInfoMapper;
import com.dazhong.transportation.vlms.model.VehicleInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleInfoDynamicSqlSupport.*;
import static com.dazhong.transportation.vlms.mapper.VehicleInfoDynamicSqlSupport.updateOperName;

@Mapper
public interface VehicleInfoExtendMapper extends VehicleInfoMapper {

    default int insertSelectiveWithId(VehicleInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleInfo, c ->
                c.map(id).toPropertyWhenPresent("id", record::getId)
                        .map(vin).toPropertyWhenPresent("vin", record::getVin)
                        .map(vehicleAssetId).toPropertyWhenPresent("vehicleAssetId", record::getVehicleAssetId)
                        .map(engineNo).toPropertyWhenPresent("engineNo", record::getEngineNo)
                        .map(engineModel).toPropertyWhenPresent("engineModel", record::getEngineModel)
                        .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", record::getVehicleModelId)
                        .map(licensePlate).toPropertyWhenPresent("licensePlate", record::getLicensePlate)
                        .map(useQuotaType).toPropertyWhenPresent("useQuotaType", record::getUseQuotaType)
                        .map(quotaType).toPropertyWhenPresent("quotaType", record::getQuotaType)
                        .map(vehicleColorId).toPropertyWhenPresent("vehicleColorId", record::getVehicleColorId)
                        .map(interiorColor).toPropertyWhenPresent("interiorColor", record::getInteriorColor)
                        .map(supplierId).toPropertyWhenPresent("supplierId", record::getSupplierId)
                        .map(isRepurchase).toPropertyWhenPresent("isRepurchase", record::getIsRepurchase)
                        .map(repurchaseDate).toPropertyWhenPresent("repurchaseDate", record::getRepurchaseDate)
                        .map(repurchaseRequirements).toPropertyWhenPresent("repurchaseRequirements", record::getRepurchaseRequirements)
                        .map(usageAgeLimit).toPropertyWhenPresent("usageAgeLimit", record::getUsageAgeLimit)
                        .map(depreciationAgeLimit).toPropertyWhenPresent("depreciationAgeLimit", record::getDepreciationAgeLimit)
                        .map(realRetirementDate).toPropertyWhenPresent("realRetirementDate", record::getRealRetirementDate)
                        .map(purchasePrice).toPropertyWhenPresent("purchasePrice", record::getPurchasePrice)
                        .map(purchaseTax).toPropertyWhenPresent("purchaseTax", record::getPurchaseTax)
                        .map(licensePlatePrice).toPropertyWhenPresent("licensePlatePrice", record::getLicensePlatePrice)
                        .map(licensePlateOtherPrice).toPropertyWhenPresent("licensePlateOtherPrice", record::getLicensePlateOtherPrice)
                        .map(upholsterPrice).toPropertyWhenPresent("upholsterPrice", record::getUpholsterPrice)
                        .map(totalPrice).toPropertyWhenPresent("totalPrice", record::getTotalPrice)
                        .map(remainPrice).toPropertyWhenPresent("remainPrice", record::getRemainPrice)
                        .map(secondHandPrice).toPropertyWhenPresent("secondHandPrice", record::getSecondHandPrice)
                        .map(propertyStatus).toPropertyWhenPresent("propertyStatus", record::getPropertyStatus)
                        .map(productLine).toPropertyWhenPresent("productLine", record::getProductLine)
                        .map(businessLine).toPropertyWhenPresent("businessLine", record::getBusinessLine)
                        .map(operatingStatus).toPropertyWhenPresent("operatingStatus", record::getOperatingStatus)
                        .map(subscriptionCompanyCode).toPropertyWhenPresent("subscriptionCompanyCode", record::getSubscriptionCompanyCode)
                        .map(subscriptionCompanyName).toPropertyWhenPresent("subscriptionCompanyName", record::getSubscriptionCompanyName)
                        .map(quotaAssetCompanyId).toPropertyWhenPresent("quotaAssetCompanyId", record::getQuotaAssetCompanyId)
                        .map(assetCompanyId).toPropertyWhenPresent("assetCompanyId", record::getAssetCompanyId)
                        .map(ownOrganizationId).toPropertyWhenPresent("ownOrganizationId", record::getOwnOrganizationId)
                        .map(usageOrganizationId).toPropertyWhenPresent("usageOrganizationId", record::getUsageOrganizationId)
                        .map(belongingTeam).toPropertyWhenPresent("belongingTeam", record::getBelongingTeam)
                        .map(obtainWayId).toPropertyWhenPresent("obtainWayId", record::getObtainWayId)
                        .map(areaId).toPropertyWhenPresent("areaId", record::getAreaId)
                        .map(depreciationDataId).toPropertyWhenPresent("depreciationDataId", record::getDepreciationDataId)
                        .map(latestPosition).toPropertyWhenPresent("latestPosition", record::getLatestPosition)
                        .map(latestTotalMileage).toPropertyWhenPresent("latestTotalMileage", record::getLatestTotalMileage)
                        .map(usageIdRegistrationCard).toPropertyWhenPresent("usageIdRegistrationCard", record::getUsageIdRegistrationCard)
                        .map(vehicleTypeRegistrationCard).toPropertyWhenPresent("vehicleTypeRegistrationCard", record::getVehicleTypeRegistrationCard)
                        .map(registrationDateRegistrationCard).toPropertyWhenPresent("registrationDateRegistrationCard", record::getRegistrationDateRegistrationCard)
                        .map(issuanceDateRegistrationCard).toPropertyWhenPresent("issuanceDateRegistrationCard", record::getIssuanceDateRegistrationCard)
                        .map(retirementDateRegistrationCard).toPropertyWhenPresent("retirementDateRegistrationCard", record::getRetirementDateRegistrationCard)
                        .map(annualInspectionDueDateRegistrationCard).toPropertyWhenPresent("annualInspectionDueDateRegistrationCard", record::getAnnualInspectionDueDateRegistrationCard)
                        .map(fileNumber).toPropertyWhenPresent("fileNumber", record::getFileNumber)
                        .map(productDate).toPropertyWhenPresent("productDate", record::getProductDate)
                        .map(issuanceDate).toPropertyWhenPresent("issuanceDate", record::getIssuanceDate)
                        .map(certificateNumber).toPropertyWhenPresent("certificateNumber", record::getCertificateNumber)
                        .map(vehicleLicenseUrl).toPropertyWhenPresent("vehicleLicenseUrl", record::getVehicleLicenseUrl)
                        .map(certificateOwnershipUrl).toPropertyWhenPresent("certificateOwnershipUrl", record::getCertificateOwnershipUrl)
                        .map(certificateConformityUrl).toPropertyWhenPresent("certificateConformityUrl", record::getCertificateConformityUrl)
                        .map(vehicleInvoiceUrl).toPropertyWhenPresent("vehicleInvoiceUrl", record::getVehicleInvoiceUrl)
                        .map(purchaseTaxUrl).toPropertyWhenPresent("purchaseTaxUrl", record::getPurchaseTaxUrl)
                        .map(operatingPermitUrl).toPropertyWhenPresent("operatingPermitUrl", record::getOperatingPermitUrl)
                        .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
                        .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
                        .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
                        .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
                        .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
                        .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
                        .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }


    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SearchInboundVehicleResult", value = {
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_asset_id", property="vehicleAssetId", jdbcType=JdbcType.VARCHAR),
            @Result(column="engine_no", property="engineNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="engine_model", property="engineModel", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_type", property="vehicleType", jdbcType=JdbcType.VARCHAR),
            @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_color_id", property="vehicleColorId", jdbcType=JdbcType.INTEGER),
            @Result(column="interior_color", property="interiorColor", jdbcType=JdbcType.VARCHAR),
            @Result(column="supplier_id", property="supplierId", jdbcType=JdbcType.INTEGER),
            @Result(column="is_repurchase", property="isRepurchase", jdbcType=JdbcType.INTEGER),
            @Result(column="repurchase_date", property="repurchaseDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="repurchase_requirements", property="repurchaseRequirements", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_no", property="vehicleModelNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="order_date", property="orderDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="receipt_date", property="receiptDate", jdbcType=JdbcType.TIMESTAMP),
    })
    List<InboundVehicleResponse> searchInboundVehicleList(SelectStatementProvider selectStatement);


    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SearchAssetVehicleResult", value = {
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_asset_id", property="vehicleAssetId", jdbcType=JdbcType.VARCHAR),
            @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
            @Result(column="property_status", property="propertyStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
            @Result(column="business_line", property="businessLine", jdbcType=JdbcType.INTEGER),
            @Result(column="operating_status", property="operatingStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
            @Result(column="vehicle_abbreviation_id", property="vehicleAbbreviationId", jdbcType=JdbcType.INTEGER),
            @Result(column="assess_passenger", property="assessPassenger", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_color_id", property="vehicleColorId", jdbcType=JdbcType.INTEGER),
            @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
            @Result(column="own_organization_id", property="ownOrganizationId", jdbcType=JdbcType.BIGINT),
            @Result(column="usage_organization_id", property="usageOrganizationId", jdbcType=JdbcType.BIGINT),
            @Result(column="belonging_team", property="belongingTeam", jdbcType=JdbcType.VARCHAR),
            @Result(column="obtain_way_id", property="obtainWayId", jdbcType=JdbcType.INTEGER),
            @Result(column="annual_inspection_due_date_registration_card", property="annualInspectionDueDateRegistrationCard", jdbcType=JdbcType.DATE),
            @Result(column="registration_date_registration_card", property="registrationDateRegistrationCard", jdbcType=JdbcType.DATE),
    })
    List<VehicleListResponse> searchAssetVehicleList(SelectStatementProvider selectStatement);

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="QueryAssetVehicleResult", value = {
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_asset_id", property="vehicleAssetId", jdbcType=JdbcType.VARCHAR),
            @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
            @Result(column="property_status", property="propertyStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
            @Result(column="business_line", property="businessLine", jdbcType=JdbcType.INTEGER),
            @Result(column="operating_status", property="operatingStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
            @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
            @Result(column="own_organization_id", property="ownOrganizationId", jdbcType=JdbcType.BIGINT),
            @Result(column="usage_organization_id", property="usageOrganizationId", jdbcType=JdbcType.BIGINT),
            @Result(column="belonging_team", property="belongingTeam", jdbcType=JdbcType.VARCHAR),
            @Result(column="obtain_way_id", property="obtainWayId", jdbcType=JdbcType.INTEGER),
            @Result(column="receipt_date", property="receiptDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="annual_inspection_due_date_registration_card", property="annualInspectionDueDateRegistrationCard", jdbcType=JdbcType.VARCHAR),
            @Result(column="registration_date_registration_card", property="registrationDateRegistrationCard", jdbcType=JdbcType.VARCHAR),
    })
    List<VehicleListResponse> queryAssetVehicleList(SelectStatementProvider selectStatement);


    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SearchVehicleTransferFixedResult", value = {
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_asset_id", property="vehicleAssetId", jdbcType=JdbcType.VARCHAR),
            @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
            @Result(column="property_status", property="propertyStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
            @Result(column="business_line", property="businessLine", jdbcType=JdbcType.INTEGER),
            @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
            @Result(column="own_organization_id", property="ownOrganizationId", jdbcType=JdbcType.BIGINT),
            @Result(column="usage_organization_id", property="usageOrganizationId", jdbcType=JdbcType.BIGINT),
            @Result(column="usage_age_limit", property="usageAgeLimit", jdbcType=JdbcType.INTEGER),
            @Result(column="depreciation_age_limit", property="depreciationAgeLimit", jdbcType=JdbcType.INTEGER),
            @Result(column="purchase_price", property="purchasePrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="purchase_tax", property="purchaseTax", jdbcType=JdbcType.DECIMAL),
            @Result(column="license_plate_price", property="licensePlatePrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="license_plate_other_price", property="licensePlateOtherPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="upholster_price", property="upholsterPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="total_price", property="totalPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="receipt_date", property="receiptDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="approval_number", property="approvalNumber", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
    })
    List<VehicleTransferFixedResponse> searchVehicleTransferFixed(SelectStatementProvider selectStatement);



    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="ExportAssetVehicleInfoResult", value = {
            // 车辆基础数据
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="engine_no", property="engineNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="engine_model", property="engineModel", jdbcType=JdbcType.VARCHAR),
            @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
            @Result(column="quota_type", property="quotaType", jdbcType=JdbcType.INTEGER),
            @Result(column="quota_asset_company_id", property="quotaAssetCompanyId", jdbcType=JdbcType.INTEGER),
            @Result(column="property_status", property="propertyStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="usage_age_limit", property="usageAgeLimit", jdbcType=JdbcType.INTEGER),
            @Result(column="remain_price", property="remainPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
            @Result(column="own_organization_id", property="ownOrganizationId", jdbcType=JdbcType.BIGINT),
            @Result(column="usage_organization_id", property="usageOrganizationId", jdbcType=JdbcType.BIGINT),
            @Result(column="belonging_team", property="belongingTeam", jdbcType=JdbcType.VARCHAR),
            @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
            @Result(column="business_line", property="businessLine", jdbcType=JdbcType.INTEGER),
            @Result(column="operating_status", property="operatingStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="usage_id_registration_card", property="usageIdRegistrationCard", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_type_registration_card", property="vehicleTypeRegistrationCard", jdbcType=JdbcType.VARCHAR),
            @Result(column="retirement_date_registration_card", property="retirementDateRegistrationCard", jdbcType=JdbcType.DATE),

            // 旧系统数据
            @Result(column="operate_type_id", property="operateTypeId", jdbcType=JdbcType.INTEGER),
            @Result(column="operating_no", property="operatingNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE),
            @Result(column="license_date", property="licenseDate", jdbcType=JdbcType.DATE),

            // 车型信息
            @Result(column="vehicle_model_no", property="vehicleModelNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
            @Result(column="financial_model_name", property="financialModelName", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_abbreviation_id", property="vehicleAbbreviationId", jdbcType=JdbcType.INTEGER),
            @Result(column="assess_passenger", property="assessPassenger", jdbcType=JdbcType.INTEGER),
            @Result(column="bus_assess_passenger", property="busAssessPassenger", jdbcType=JdbcType.INTEGER),
            @Result(column="gas_type_id", property="gasTypeId", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_param", property="wheelParam", jdbcType=JdbcType.VARCHAR),
            @Result(column="exhaust_id", property="exhaustId", jdbcType=JdbcType.INTEGER),
            @Result(column="out_length", property="outLength", jdbcType=JdbcType.INTEGER),
            @Result(column="total_mass", property="totalMass", jdbcType=JdbcType.INTEGER),
            @Result(column="fuel_tank_capacity", property="fuelTankCapacity", jdbcType=JdbcType.INTEGER),
            @Result(column="battery_capacity", property="batteryCapacity", jdbcType=JdbcType.DECIMAL),
            @Result(column="engine_model_no", property="engineModelNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="real_retirement_date", property="realRetirementDate", jdbcType=JdbcType.DATE),
            // 折旧信息
            @Result(column="original_amount", property="originalAmount", jdbcType=JdbcType.DECIMAL),
            @Result(column="depreciation_months", property="depreciationMonths", jdbcType=JdbcType.INTEGER),
            @Result(column="depreciation_start_date", property="depreciationStartDate", jdbcType=JdbcType.DATE),
            @Result(column="current_month", property="currentMonth", jdbcType=JdbcType.VARCHAR),
            @Result(column="accumulated_depreciation_months", property="accumulatedDepreciationMonths", jdbcType=JdbcType.INTEGER),
            @Result(column="depreciation_amount", property="depreciationAmount", jdbcType=JdbcType.DECIMAL),
            @Result(column="remaining_residual_value", property="remainingResidualValue", jdbcType=JdbcType.DECIMAL)
    })
    List<ExportAssetVehicleInfoDto> exportAssetVehicleInfo(SelectStatementProvider selectStatement);

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SyncAssetVehicleInfoResult", value = {
            // 车辆基础数据 (vehicleInfo)
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT),
            @Result(column="id", property="cardId", jdbcType=JdbcType.BIGINT),
            @Result(column="license_plate", property="licenseTag", jdbcType=JdbcType.VARCHAR),
            @Result(column="license_date", property="licenseDate", jdbcType=JdbcType.DATE),
            @Result(column="usage_id_registration_card", property="licenseTypeId", jdbcType=JdbcType.INTEGER),
            @Result(column="remain_price", property="remainFee", jdbcType=JdbcType.DECIMAL),
            @Result(column="second_hand_price", property="soldPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="product_date", property="produceDate", jdbcType=JdbcType.DATE),
            @Result(column="total_price", property="carPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="purchase_price", property="carPurePrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="purchase_tax", property="purchaseCost", jdbcType=JdbcType.DECIMAL),
            @Result(column="engine_no", property="engineNumber", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="frameNumber", jdbcType=JdbcType.VARCHAR),
            @Result(column="real_retirement_date", property="rejectDate", jdbcType=JdbcType.DATE),

            // 旧系统数据 (vehicleManagementLegacyInfo)
            @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.DATE),
            @Result(column="start_date", property="verifyDate", jdbcType=JdbcType.DATE),
            @Result(column="upholster_price", property="upholsterFee", jdbcType=JdbcType.DECIMAL),
            @Result(column="license_plate_other_price", property="licenseOtherPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="usage_organization_id", property="companyId", jdbcType=JdbcType.INTEGER),

            // 车型信息 (vehicleModelInfo)
            @Result(column="vehicle_abbreviation_id", property="carNameId", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
            @Result(column="financial_model_name", property="financialModelName", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
            @Result(column="vehicle_color_id", property="carColorId", jdbcType=JdbcType.INTEGER),
            @Result(column="assess_passenger", property="seatNumber", jdbcType=JdbcType.INTEGER),
            @Result(column="manufacturer_id", property="productId", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_quantity", property="tyreNumber1", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_quantity", property="tyreNumber2", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_param", property="tyreType", jdbcType=JdbcType.VARCHAR),
            @Result(column="out_length", property="length", jdbcType=JdbcType.INTEGER),
            @Result(column="out_width", property="width", jdbcType=JdbcType.INTEGER),
            @Result(column="out_height", property="height", jdbcType=JdbcType.INTEGER),
            @Result(column="gas_type_id", property="oilTradeId", jdbcType=JdbcType.INTEGER),
            @Result(column="gas_type", property="oilTrade", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_type_id", property="carClassId", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_base_1", property="wheelDistance1", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_base_2", property="wheelDistance2", jdbcType=JdbcType.INTEGER),
            @Result(column="assess_mass", property="netWeight", jdbcType=JdbcType.INTEGER),
            @Result(column="total_mass", property="totalWeight", jdbcType=JdbcType.INTEGER)
    })
    List<SyncAssetVehicleInfoDto> searchAssetVehicleSyncData(SelectStatementProvider selectStatement);

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SyncDatabaseVehicleResult", value = {
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT),
            @Result(column="license_plate", property="license", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="frameNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="engine_no", property="engineNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="product_date", property="productDate", jdbcType=JdbcType.DATE),
            @Result(column="purchase_date", property="purchaseDate", jdbcType=JdbcType.DATE),
            @Result(column="real_retirement_date", property="retireDate", jdbcType=JdbcType.DATE),
            @Result(column="usage_age_limit", property="usageAgeLimit", jdbcType=JdbcType.INTEGER),
            @Result(column="depreciation_age_limit", property="depreciationAgeLimit", jdbcType=JdbcType.INTEGER),
            @Result(column="obtain_way_id", property="obtainWayId", jdbcType=JdbcType.INTEGER),
            @Result(column="usage_id_registration_card", property="usageId", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_color_id", property="vehicleColorId", jdbcType=JdbcType.INTEGER),
            @Result(column="area_id", property="areaId", jdbcType=JdbcType.INTEGER),
            @Result(column="supplier_id", property="supplierId", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
            @Result(column="certificate_number", property="vehicleRegisterNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="force_scrap_date", property="forceScrapDate", jdbcType=JdbcType.DATE),
            @Result(column="own_organization_id", property="ownOrganizationId", jdbcType=JdbcType.BIGINT),
            @Result(column="usage_organization_id", property="usageOrganizationId", jdbcType=JdbcType.BIGINT),
            @Result(column="property_status", property="propertyStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="relate_asset_id", property="vehicleAssetId", jdbcType=JdbcType.BIGINT),
            @Result(column="vehicle_asset_id", property="archivesNo", jdbcType=JdbcType.VARCHAR),


            @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE),
            @Result(column="license_date", property="licenseDate", jdbcType=JdbcType.DATE),
            @Result(column="operating_no", property="operatingNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
            @Result(column="asset_owner_id", property="assetOwnerId", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_category_id", property="vehicleCategoryId", jdbcType=JdbcType.INTEGER),
            @Result(column="operate_type_id", property="operateTypeId", jdbcType=JdbcType.INTEGER),
            @Result(column="contract_type_id", property="contractTypeId", jdbcType=JdbcType.INTEGER),
            @Result(column="operation_category_id", property="operationCategoryId", jdbcType=JdbcType.INTEGER),
            @Result(column="operation_start_date", property="putOperateDate", jdbcType=JdbcType.VARCHAR),
            @Result(column="company_owner_id", property="companyOwnerId", jdbcType=JdbcType.INTEGER),
            @Result(column="from_company", property="fromCompany", jdbcType=JdbcType.VARCHAR),
    })
    List<SyncDatabaseVehicleDto> searchVehicleDatabaseSyncData(SelectStatementProvider selectStatement);


    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SyncDatabaseVehicleAssetResult", value = {
            @Result(column="id", property="vehicleId", jdbcType=JdbcType.BIGINT),
            @Result(column="relate_asset_id", property="id", jdbcType=JdbcType.BIGINT),
            @Result(column="purchase_price", property="purchasePrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="purchase_tax", property="purchaseTax", jdbcType=JdbcType.DECIMAL),
            @Result(column="license_plate_price", property="licensePrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="license_plate_other_price", property="licenseOtherPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="upholster_price", property="upholsterPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="total_price", property="totalPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="remain_price", property="remainPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="second_hand_price", property="secondHandPrice", jdbcType=JdbcType.DECIMAL)
    })
    List<SyncDatabaseVehicleAssetDto> searchVehicleAssetDatabaseSyncData(SelectStatementProvider selectStatement);
}